import { BrowserWindow, screen, app, shell, nativeImage } from 'electron';
import path from 'path';
import fs from 'fs';
import { debounce } from 'lodash';
import { getWindowState, setWindowState } from './index';

// Extend the App interface
interface ExtendedApp extends Electron.App {
  isQuitting?: boolean;
}

interface WindowBounds {
  width: number;
  height: number;
  x: number;
  y: number;
}

let settingsWindow: BrowserWindow | null = null;
let isFirstLaunch = false;
let normalBounds: WindowBounds = { width: 1275, height: 780, x: 0, y: 0 };
let lastActiveDisplay: { id: number; scaleFactor: number } | null = null;
let isResizingOrMovingFromMaximize = false;
let isHandlingMaximizeOperation = false;
let lastLoggedDisplayId: number | null = null;
let displayLogTimeout: NodeJS.Timeout | null = null;

// Create a debounced save window state function using lodash
const debouncedSaveWindowState = debounce(() => {
  if (!settingsWindow) return;
  
  const isMaximised = settingsWindow.isMaximized();
  const currentDisplay = screen.getDisplayMatching(settingsWindow.getBounds());
  
  lastActiveDisplay = {
    id: currentDisplay.id,
    scaleFactor: currentDisplay.scaleFactor
  };
  
  if (isMaximised) {
    setWindowState('settings', {
      ...normalBounds,
      isMaximised: true,
      hasBeenSaved: true,
      displayId: currentDisplay.id,
      displayScaleFactor: currentDisplay.scaleFactor
    });
  } else {
    if (!isResizingOrMovingFromMaximize) {
      const currentBounds = settingsWindow.getBounds() as WindowBounds;
      normalBounds = currentBounds;
    }
    
    setWindowState('settings', {
      ...normalBounds,
      isMaximised: false,
      hasBeenSaved: true,
      displayId: currentDisplay.id,
      displayScaleFactor: currentDisplay.scaleFactor
    });
  }
}, 300); // Reduced from 500ms to 300ms for better responsiveness

// Remove the inlined store logic here (now imported from './index')

export function createSettingsWindow(): BrowserWindow {
  const storedBounds = getWindowState('settings');
  
  isFirstLaunch = !storedBounds.hasBeenSaved;
  console.log('[SETTINGS] First launch?', isFirstLaunch);

  const { width = 1275, height = 780, x = 0, y = 0, displayId, displayScaleFactor } = storedBounds;
  
  if (displayId !== undefined && displayScaleFactor !== undefined) {
    lastActiveDisplay = {
      id: displayId,
      scaleFactor: displayScaleFactor
    };
  }
  
  const hasValidPosition = !isFirstLaunch && 
                          x !== undefined && 
                          y !== undefined && 
                          x > -10000 && 
                          y > -10000;
  
  if (!isFirstLaunch) {
    normalBounds = { width, height, x, y };
  }
  
  // Use stored bounds directly
  let adjustedBounds: WindowBounds = { width, height, x, y };
  
  // Get the current system scale factor
  const currentDisplay = screen.getPrimaryDisplay();
  const currentScaleFactor = currentDisplay.scaleFactor;
  const storedScaleFactor = displayScaleFactor || 1;
  
  // Counteract Electron's automatic scaling by applying the inverse scale
  // This is specifically to fix the 20% reduction when reopening the app
  if (!isFirstLaunch && storedScaleFactor !== undefined) {
    const compensationFactor = 1 / (currentScaleFactor / storedScaleFactor);
    adjustedBounds.width = Math.round(adjustedBounds.width / compensationFactor);
    adjustedBounds.height = Math.round(adjustedBounds.height / compensationFactor);
  }
  
  settingsWindow = new BrowserWindow({
    width: adjustedBounds.width,
    height: adjustedBounds.height,
    minWidth: 1050,
    minHeight: 780,
    ...(hasValidPosition ? { x: adjustedBounds.x, y: adjustedBounds.y } : { center: true }),
    icon: nativeImage.createFromPath(
      app.isPackaged
        ? path.join(process.resourcesPath, 'icon.ico')
        : path.join(process.cwd(), 'src', 'assets', 'icon.ico')
    ),
    webPreferences: {
      preload: path.join(__dirname, '../preload.js'),
      contextIsolation: true,
      nodeIntegration: false,
      backgroundThrottling: false
    },
    titleBarStyle: 'hidden',
    titleBarOverlay: {
      color: '#00000000',
      symbolColor: '#00000000',
      height: 33,
    },
    show: false
  });
  
  if (!hasValidPosition) {
    const bounds = settingsWindow.getBounds() as WindowBounds;
    normalBounds = bounds;
    const currentDisplay = screen.getDisplayMatching(bounds);
    
    setWindowState('settings', { 
      ...bounds, 
      isMaximised: false,
      hasBeenSaved: true,
      displayId: currentDisplay.id,
      displayScaleFactor: currentDisplay.scaleFactor
    });
    
    lastActiveDisplay = {
      id: currentDisplay.id,
      scaleFactor: currentDisplay.scaleFactor
    };
  }

  settingsWindow.once('ready-to-show', () => {
    // Window is ready but we control showing manually from main.ts
    console.log('[SETTINGS] Window ready');
  });

  settingsWindow.on('close', (e) => {
    if (!(app as ExtendedApp).isQuitting) {
      e.preventDefault();
      settingsWindow?.hide();
    } else if (settingsWindow) {
      // Flush any pending debounced saves
      debouncedSaveWindowState.flush();
      
      const isMaximised = settingsWindow.isMaximized();
      const currentDisplay = screen.getDisplayMatching(settingsWindow.getBounds());
      
      if (isMaximised) {
        setWindowState('settings', {
          ...normalBounds,
          isMaximised: true,
          hasBeenSaved: true,
          displayId: currentDisplay.id,
          displayScaleFactor: currentDisplay.scaleFactor
        });
      } else {
        const bounds = settingsWindow.getBounds() as WindowBounds;
        setWindowState('settings', {
          ...bounds,
          isMaximised: false,
          hasBeenSaved: true,
          displayId: currentDisplay.id,
          displayScaleFactor: currentDisplay.scaleFactor
        });
      }
    }
  });

  settingsWindow.on('maximize', () => {
    if (settingsWindow) {
      isHandlingMaximizeOperation = true;
      isResizingOrMovingFromMaximize = true;
      
      if (!settingsWindow.isMaximized()) {
        const preBounds = settingsWindow.getBounds() as WindowBounds;
        normalBounds = preBounds;
        
        const currentDisplay = screen.getDisplayMatching(preBounds);
        lastActiveDisplay = {
          id: currentDisplay.id,
          scaleFactor: currentDisplay.scaleFactor
        };
      }
      
      const currentDisplay = screen.getDisplayMatching(settingsWindow.getBounds());
      setWindowState('settings', { 
        ...normalBounds,
        isMaximised: true,
        hasBeenSaved: true,
        displayId: currentDisplay.id,
        displayScaleFactor: currentDisplay.scaleFactor
      });
      
      // Use a single timeout with a more predictable approach
      setTimeout(() => {
        isHandlingMaximizeOperation = false;
        isResizingOrMovingFromMaximize = false;
      }, 1000);
    }
  });

  settingsWindow.on('unmaximize', () => {
    if (settingsWindow) {
      isHandlingMaximizeOperation = true;
      isResizingOrMovingFromMaximize = true;
      
      const currentDisplay = screen.getDisplayMatching(settingsWindow.getBounds());
      let boundsToRestore = { ...normalBounds };
      
      setWindowState('settings', { 
        ...boundsToRestore,
        isMaximised: false,
        hasBeenSaved: true,
        displayId: currentDisplay.id,
        displayScaleFactor: currentDisplay.scaleFactor
      });
      
      settingsWindow.setBounds(boundsToRestore);
      normalBounds = boundsToRestore;
      
      lastActiveDisplay = {
        id: currentDisplay.id,
        scaleFactor: currentDisplay.scaleFactor
      };
      
      // Use a single timeout with a more predictable approach
      setTimeout(() => {
        isHandlingMaximizeOperation = false;
        isResizingOrMovingFromMaximize = false;
      }, 1000);
    }
  });
  
  screen.on('display-added', (_event, display) => {
    console.log('[SETTINGS] Display added:', display.id);
  });
  
  screen.on('display-removed', (_event, display) => {
    console.log('[SETTINGS] Display removed:', display.id);
  });
  
  screen.on('display-metrics-changed', (_event, display, changedMetrics) => {
    console.log('[SETTINGS] Display metrics changed:', display.id, changedMetrics);
  });

  settingsWindow.on('resize', () => {
    if (settingsWindow && !settingsWindow.isMaximized() && !isResizingOrMovingFromMaximize && !isHandlingMaximizeOperation) {
      debouncedSaveWindowState();
    }
  });

  settingsWindow.on('move', () => {
    if (settingsWindow && !settingsWindow.isMaximized() && !isResizingOrMovingFromMaximize && !isHandlingMaximizeOperation) {
      const currentDisplay = screen.getDisplayMatching(settingsWindow.getBounds());
      
      if (lastActiveDisplay !== null && lastActiveDisplay.id !== currentDisplay.id) {
        if (lastLoggedDisplayId !== currentDisplay.id) {
          if (displayLogTimeout) {
            clearTimeout(displayLogTimeout);
          }
          
          displayLogTimeout = setTimeout(() => {
            console.log('[SETTINGS] Moved to different display:', 
                       currentDisplay.id,
                       `(scale factor: ${currentDisplay.scaleFactor})`,
                       'from', 
                       lastActiveDisplay!.id,
                       `(scale factor: ${lastActiveDisplay!.scaleFactor})`);
            
            lastLoggedDisplayId = currentDisplay.id;
          }, 200);
        }
      }
      
      debouncedSaveWindowState();
    }
  });

  // Better development detection - check multiple indicators
  const isDev = process.env.NODE_ENV === 'development' || 
                !app.isPackaged ||
                process.env.ELECTRON_IS_DEV === '1';
  
  console.log('[SETTINGS] Environment detection:', {
    NODE_ENV: process.env.NODE_ENV,
    isPackaged: app.isPackaged,
    ELECTRON_IS_DEV: process.env.ELECTRON_IS_DEV,
    isDev
  });

  // Only open dev tools in development
  if (isDev) {
    settingsWindow.webContents.openDevTools();
  }

  if (isDev) {
    console.log('[SETTINGS] Running in development mode, loading from Vite dev server');
    
    // Use ready-to-show event to ensure we don't show until content is loaded
    settingsWindow.once('ready-to-show', () => {
      console.log('[SETTINGS] Dev server content loaded, window ready');
    });
    
    settingsWindow.loadURL('http://localhost:5173/index.html')
      .catch(err => {
        console.error('[SETTINGS] Failed to load from dev server:', err);
                 // Fallback to built files if dev server fails
         console.log('[SETTINGS] Falling back to built files');
         const htmlPath = path.join(__dirname, '../../../renderer/index.html');
         if (fs.existsSync(htmlPath) && settingsWindow) {
           settingsWindow.loadFile(htmlPath);
         }
      });
  } else {
    console.log('[SETTINGS] Running in production mode, loading from built files');
    const htmlPath = path.join(__dirname, '../../../renderer/index.html');
    
    if (!fs.existsSync(htmlPath)) {
      console.error('[SETTINGS] Could not find HTML file at:', htmlPath);
      throw new Error(`HTML file not found at ${htmlPath}`);
    }
    
    settingsWindow.loadFile(htmlPath);
  }

  settingsWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  return settingsWindow;
}

export function getSettingsWindow(): BrowserWindow | null {
  return settingsWindow;
}

export function showSettingsWindow(): void {
  if (settingsWindow) {
    console.log('[SETTINGS] Showing window after initial sync completed');
    settingsWindow.show();
    
    // Apply maximized state if needed
    const storedBounds = getWindowState('settings');
    if (storedBounds.isMaximised) {
      settingsWindow.maximize();
    }
  } else {
    console.warn('[SETTINGS] Cannot show window - not created yet');
  }
}