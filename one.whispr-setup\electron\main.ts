// Add electron-squirrel-startup at the very top
// eslint-disable-next-line @typescript-eslint/no-var-requires
if (require('electron-squirrel-startup')) 
  process.exit(0);

import { app } from 'electron';
import { createLauncherWindow, showLauncherWindow } from './managers/window';
import { launcher } from './managers/launcher';
import { SETUP_UPDATES } from '../src/lib/constants';

// --- Electron Updater Integration ---
import { autoUpdater } from 'electron-updater';

// Configure autoUpdater
autoUpdater.setFeedURL({
  provider: 'generic',
  url: SETUP_UPDATES.baseUrl
});

// Development mode is determined by app.isPackaged - no IPC needed

// Setup app ready handler
app.whenReady().then(async () => {
  console.log('[LAUNCHER] App ready, initializing launcher...');
  
  // Check for updates
  autoUpdater.checkForUpdatesAndNotify();
  autoUpdater.on('update-available', (info) => {
    console.log('[UPDATER] Update available!', info);
  });
  autoUpdater.on('update-not-available', (info) => {
    console.log('[UPDATER] No update available.', info);
  });
  autoUpdater.on('error', (err) => {
    console.error('[UPDATER] Error:', err);
  });
  autoUpdater.on('download-progress', (progress) => {
    console.log('[UPDATER] Download progress:', Math.round(progress.percent), '%');
  });
  autoUpdater.on('update-downloaded', (info) => {
    console.log('[UPDATER] Update downloaded. Will install on quit.', info);
  });

  // Create and show the launcher window
  createLauncherWindow();
  showLauncherWindow();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Clean up handlers on app quit
app.on('before-quit', () => {
  console.log('[LAUNCHER] Cleaning up before quit...');
  launcher.cleanup();
});