{"name": "one.whispr-app", "version": "1.0.0", "description": "One Whispr - AI-powered transcription and productivity app", "author": "One Whispr Team", "private": true, "main": ".dist/main/electron/main.js", "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.5.0", "react-hook-form": "^7.55.0", "clsx": "^2.1.1", "class-variance-authority": "^0.7.1", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "zod": "^3.24.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "motion": "^12.6.3", "electron-store": "^8.2.0", "electron-updater": "^6.3.4", "ws": "^8.18.2", "better-sqlite3": "^11.9.1", "lodash": "^4.17.21", "nanoid": "^3.3.7"}}