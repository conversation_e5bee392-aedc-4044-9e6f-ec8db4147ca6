import path from 'path';
import fs from 'fs';
import ora from 'ora';
import sevenBin from '7zip-bin';
import {
  getPaths,
  runCommand,
  runPythonProcess,
  checkPyInstaller,
  copyFolderRecursiveSync,
  countFiles,
  killPythonProcesses
} from './python-utils';


/**
 * Get 7zip command path from npm package
 */
function get7zipCommand(): string {
  // Use the 7zip-bin npm package which provides cross-platform 7zip binaries
  return sevenBin.path7za;
}

/**
 * Get version from package.json (same as deploy.yaml)
 */
function getVersion(): string {
  try {
    // Read package.json version (same as deploy.yaml: node -p "require('./package.json').version")
    const packageJsonPath = path.join(__dirname, '../../package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const version = packageJson.version;
    console.log(`[VERSION] Using package.json version: ${version}`);
    return version;
  } catch (error) {
    console.warn('[VERSION] Could not read package.json, using fallback');
    return '1.0.0';
  }
}

/**
 * Clean up .pyc files and __pycache__ directories from source directory
 */
function cleanupPycFiles(projectRoot: string): void {
  const pythonDir = path.join(projectRoot, 'python');

  if (!fs.existsSync(pythonDir)) {
    return;
  }

  function cleanupDirectory(dir: string): void {
    try {
      const items = fs.readdirSync(dir);

      for (const item of items) {
        const itemPath = path.join(dir, item);
        const stats = fs.statSync(itemPath);

        if (stats.isDirectory()) {
          if (item === '__pycache__') {
            // Remove entire __pycache__ directory
            fs.rmSync(itemPath, { recursive: true, force: true });
            console.log(`[CLEANUP] Removed __pycache__: ${itemPath}`);
          } else {
            // Recursively clean subdirectories
            cleanupDirectory(itemPath);
          }
        } else if (item.endsWith('.pyc')) {
          // Remove .pyc files
          fs.unlinkSync(itemPath);
          console.log(`[CLEANUP] Removed .pyc file: ${itemPath}`);
        }
      }
    } catch (error) {
      console.warn(`[CLEANUP] Warning: Could not clean directory ${dir}:`, error);
    }
  }

  cleanupDirectory(pythonDir);
}

/**
 * Copy only .pyc files from source to destination, preserving directory structure
 */
function copyPycFiles(sourceDir: string, destDir: string): void {
  function copyPycRecursive(src: string, dest: string) {
    const items = fs.readdirSync(src);

    for (const item of items) {
      const srcPath = path.join(src, item);
      const destPath = path.join(dest, item);
      const stats = fs.statSync(srcPath);

      if (stats.isDirectory()) {
        // Skip __pycache__ directories
        if (item === '__pycache__') {
          continue;
        }

        // Create directory and recurse
        if (!fs.existsSync(destPath)) {
          fs.mkdirSync(destPath, { recursive: true });
        }
        copyPycRecursive(srcPath, destPath);
      } else if (stats.isFile() && item.endsWith('.pyc')) {
        // Copy .pyc files
        fs.copyFileSync(srcPath, destPath);
      }
      // Skip .py files and other files
    }
  }

  if (!fs.existsSync(destDir)) {
    fs.mkdirSync(destDir, { recursive: true });
  }

  copyPycRecursive(sourceDir, destDir);
}

// Calculate directory size recursively
function getDirSize(dirPath: string): number {
  let totalSize = 0;
  
  try {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stats = fs.statSync(itemPath);
      
      if (stats.isFile()) {
        totalSize += stats.size;
      } else if (stats.isDirectory()) {
        totalSize += getDirSize(itemPath);
      }
    }
  } catch (error) {
    // Handle errors silently and return current total
  }
  
  return totalSize;
}



// Parse CLI arguments
interface CompileOptions {
  debug: boolean;
  clean: boolean;
  quickUpdate: boolean;
}

function parseArgs(): CompileOptions {
  const args = process.argv.slice(2);

  // Debug: Show what we're parsing
  console.log(`🔍 Raw arguments: ${JSON.stringify(args)}`);

  return {
    debug: args.includes('--debug') || args.includes('-d'),
    clean: !args.includes('--no-clean') && (args.includes('--clean') || args.includes('-c') || args.length === 0), // Default to true
    quickUpdate: args.includes('--quick-update') || args.includes('-q'),
  };
}

// Build Python executable with PyInstaller (always uses separated build)
async function buildPythonExecutable(options: CompileOptions = { debug: false, clean: true, quickUpdate: false }) {
  const spinner = ora('🚀 Preparing to build Python executable...').start();
  
  const paths = getPaths();
  const {
    pythonExePath,
    projectRoot,
    outputDir,
    buildDir,
    finalOutputDir
  } = paths;
  
  // Ensure the python directory exists
  if (!fs.existsSync(pythonExePath)) {
    spinner.fail('❌ Python installation not found. Please run setup-python first.');
    process.exit(1);
  }
  
  try {

    if (options.quickUpdate) {
      // Quick update mode: skip PyInstaller, only compile bytecode
      spinner.text = '⚡ Quick update mode: compiling bytecode only...';

      // Create output directories
      const updatableDir = path.join(path.dirname(finalOutputDir), 'updatable');
      const compressedDir = path.join(path.dirname(finalOutputDir), 'compressed');

      [updatableDir, compressedDir].forEach(dir => {
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }
      });

      // Clean updatable directory
      if (fs.existsSync(updatableDir)) {
        fs.rmSync(updatableDir, { recursive: true, force: true });
        fs.mkdirSync(updatableDir, { recursive: true });
      }

      await compileAndPackageBytecode(pythonExePath, updatableDir, compressedDir, finalOutputDir, spinner);

      spinner.succeed('✅ Quick update completed successfully!');
      return finalOutputDir;
    }

    // Full build mode: run PyInstaller
    // Check if PyInstaller is installed
    spinner.text = '🔍 Checking for PyInstaller...';
    const pyinstallerInstalled = await checkPyInstaller(pythonExePath);

    if (!pyinstallerInstalled) {
      spinner.text = '📦 Installing PyInstaller...';
      try {
        await runCommand(pythonExePath, [
          '-m', 'pip', 'install', 'pyinstaller==6.12.0'
        ]);
      } catch (error: any) {
        spinner.fail(`❌ Failed to install PyInstaller: ${error.message}`);
        throw error;
      }
    }

    // Kill any running Python processes on Windows
    await killPythonProcesses(spinner);
    
    // Create output directories if they don't exist
    [outputDir, buildDir].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
    
    // Clean up existing output directory if needed
    if (options.clean && fs.existsSync(finalOutputDir)) {
      spinner.text = '🧹 Cleaning up previous build files...';
      fs.rmSync(finalOutputDir, { recursive: true, force: true });
    }
    
    // Run PyInstaller with the spec file
    spinner.text = '🔨 Running PyInstaller (this may take several minutes)...';
    
    // Force spinner to stop so it doesn't interfere with console output
    spinner.stop();
    console.log('🚀 Starting PyInstaller process...');
    
    // Build the PyInstaller arguments (optimizations are in the .spec file)
    const pyinstallerArgs = [
      '-m', 'PyInstaller',
      '--distpath', outputDir,
      '--workpath', buildDir,
      '--noconfirm'
    ];
    
    // Add clean flag if not in debug mode (debug flag not compatible with spec files)
    if (!options.debug) {
      pyinstallerArgs.push('--clean');
    }
    
    // Note: LZMA compression is handled by the spec file, not command line args
    // The --lzma flag is not needed when using a spec file with onefile=True
    
    // Always use separated build
    const selectedSpecFile = path.join(projectRoot, 'python', 'backend.separated.spec');

    // Verify the selected spec file exists
    if (!fs.existsSync(selectedSpecFile)) {
      throw new Error(`Spec file not found: ${selectedSpecFile}`);
    }

    console.log(`� Using separated build: ${path.basename(selectedSpecFile)}`);
    console.log(`📦 This creates exe + _internal structure with 7zip compression and encrypted updates`);
    
    pyinstallerArgs.push(selectedSpecFile);
    
    // Run PyInstaller with better output handling
    const { stderr, exitCode } = await runPythonProcess(
      pythonExePath,
      pyinstallerArgs,
      {
        env: {
          ...process.env,
          PYTHONPATH: path.join(projectRoot, 'python'),
          // Speed optimizations
          PYTHONDONTWRITEBYTECODE: '0'          // Allow bytecode caching
        },
        onStdout: (text: string) => {
          // Print directly to console
          console.log(`[PyInstaller] ${text.trim()}`);
        },
        onStderr: (text: string) => {
          // Print errors directly to console
          console.error(`[PyInstaller] ${text.trim()}`);
        }
      }
    );
    
    // Restart spinner for the remaining steps
    spinner.start();
    
    if (exitCode === 0) {
      spinner.succeed('✅ PyInstaller completed successfully!');
      
      // Handle different output types based on compression mode
      spinner.text = '📦 Processing executable output...';
      
      try {
        // Always use separated build: create base runtime + separate updatable files
        const sourceDir = path.join(outputDir, 'One Whispr Backend');

          // Verify source directory exists
          if (!fs.existsSync(sourceDir)) {
            spinner.fail(`❌ PyInstaller output not found at ${sourceDir}`);
            throw new Error(`PyInstaller output not found at ${sourceDir}`);
          }

          spinner.text = '📦 Creating separated build structure...';

          // Create the separated build structure
          const baseRuntimeDir = path.join(path.dirname(finalOutputDir), 'base-runtime');
          const updatableDir = path.join(path.dirname(finalOutputDir), 'updatable');
          const compressedDir = path.join(path.dirname(finalOutputDir), 'compressed');

          // Create directories
          [baseRuntimeDir, updatableDir, compressedDir].forEach(dir => {
            if (!fs.existsSync(dir)) {
              fs.mkdirSync(dir, { recursive: true });
            }
          });

          spinner.text = '📦 Preparing base runtime (exe + _internal)...';

          // Copy only exe and _internal to base runtime (exclude updatable folder)
          const exePath = path.join(sourceDir, 'One Whispr Backend.exe');
          const internalPath = path.join(sourceDir, '_internal');

          if (fs.existsSync(exePath)) {
            fs.copyFileSync(exePath, path.join(baseRuntimeDir, 'One Whispr Backend.exe'));
          }

          if (fs.existsSync(internalPath)) {
            copyFolderRecursiveSync(internalPath, baseRuntimeDir);
          }

          spinner.text = '🔐 Compiling Python files to bytecode...';

          // Compile Python files to .pyc bytecode for protection
          const mainPySource = path.join(projectRoot, 'python', 'main.py');
          const whisprSource = path.join(projectRoot, 'python', 'whispr');

          try {
            // Compile main.py to bytecode
            if (fs.existsSync(mainPySource)) {
              await runCommand(pythonExePath, [
                '-m', 'py_compile', mainPySource
              ]);

              // Find the generated .pyc file and copy it
              const pycacheDir = path.join(path.dirname(mainPySource), '__pycache__');
              if (fs.existsSync(pycacheDir)) {
                const pycFiles = fs.readdirSync(pycacheDir).filter(f => f.startsWith('main.') && f.endsWith('.pyc'));
                if (pycFiles.length > 0) {
                  const pycFile = path.join(pycacheDir, pycFiles[0]);
                  fs.copyFileSync(pycFile, path.join(updatableDir, 'main.pyc'));
                  spinner.info(`✅ Compiled main.py to bytecode`);
                }
              }
            }

            // Compile entire whispr directory to bytecode with parallel processing
            if (fs.existsSync(whisprSource)) {
              await runCommand(pythonExePath, [
                '-m', 'compileall', '-b', '-j', '4', whisprSource  // Use 4 parallel workers
              ]);

              // Create whispr directory in updatable folder and copy .pyc files there
              const whisprDestDir = path.join(updatableDir, 'whispr');
              if (!fs.existsSync(whisprDestDir)) {
                fs.mkdirSync(whisprDestDir, { recursive: true });
              }
              copyPycFiles(whisprSource, whisprDestDir);

              spinner.info(`✅ Compiled whispr/ directory to bytecode`);
            }

          } catch (error) {
            spinner.warn('⚠️ Bytecode compilation failed, copying source files');
            console.warn('Compilation error:', error);

            // Fallback: copy source files
            if (fs.existsSync(mainPySource)) {
              fs.copyFileSync(mainPySource, path.join(updatableDir, 'main.py'));
            }
            if (fs.existsSync(whisprSource)) {
              copyFolderRecursiveSync(whisprSource, updatableDir);
            }
          } finally {
            // Clean up .pyc files and __pycache__ directories from source
            const projectRoot = path.resolve(__dirname, '../..');
            cleanupPycFiles(projectRoot);
          }

          // Create version files before compression
          spinner.text = '📝 Creating version files...';

          // Get dynamic version
          const currentVersion = getVersion();
          const currentDate = new Date().toISOString();

          // Create runtime version file (matching deploy.yaml structure)
          const runtimeVersionPath = path.join(baseRuntimeDir, 'runtime-version.json');
          const runtimeVersion = {
            version: currentVersion,
            releaseDate: currentDate,
            releaseNotes: 'Backend runtime base package',
            downloadUrl: 'local-build',
            versionsUrl: 'local-build',
            compressionType: '7z-lzma2-ultra',
            checksum: 'local-build'
          };
          fs.writeFileSync(runtimeVersionPath, JSON.stringify(runtimeVersion, null, 2));

          // Create scripts version file (matching deploy.yaml structure)
          const scriptsVersionPath = path.join(updatableDir, 'scripts-version.json');
          const scriptsVersion = {
            version: currentVersion,
            releaseDate: currentDate,
            releaseNotes: 'Backend scripts bytecode update',
            downloadUrl: 'local-build',
            versionsUrl: 'local-build',
            updateType: 'scripts',
            compressionType: '7z',
            checksum: 'local-build'
          };
          fs.writeFileSync(scriptsVersionPath, JSON.stringify(scriptsVersion, null, 2));

          spinner.succeed(`✅ Version files created (v${currentVersion})`);

          spinner.text = '🗜️ Compressing base runtime with 7zip...';

          // Get 7zip command from npm package
          const zipCommand = get7zipCommand();
          spinner.info(`✅ Using 7zip from npm package: ${zipCommand}`);

          if (zipCommand) {
            // Create 7zip compressed base runtime
            const baseRuntimeArchive = path.join(compressedDir, 'OneWhispr-Runtime-Base.7z');

            try {
              // Use 7zip to compress base runtime with ultra settings
              await runCommand(zipCommand, [
                'a', '-t7z', '-m0=lzma2', '-mx=9', '-mfb=64', '-md=32m', '-ms=on',
                baseRuntimeArchive,
                path.join(baseRuntimeDir, '*')
              ]);

              // Verify compression
              const originalSize = getDirSize(baseRuntimeDir);
              const compressedSize = fs.statSync(baseRuntimeArchive).size;
              const ratio = ((compressedSize / originalSize) * 100).toFixed(1);

              spinner.succeed(`✅ Base runtime compressed: ${Math.round(originalSize / (1024*1024))}MB → ${Math.round(compressedSize / (1024*1024))}MB (${ratio}%)`);

            } catch (error) {
              spinner.warn('⚠️ 7zip compression failed, keeping uncompressed base runtime');
              console.warn('7zip error:', error);
              // Copy uncompressed as fallback
              copyFolderRecursiveSync(baseRuntimeDir, compressedDir);
            }
          } else {
            spinner.warn('⚠️ 7zip not found - copying uncompressed base runtime');
            spinner.info('💡 Install 7zip to enable ultra compression (70-80% size reduction)');
            copyFolderRecursiveSync(baseRuntimeDir, compressedDir);
          }

          spinner.text = '🗜️ Compressing bytecode files...';

          if (zipCommand) {
            // Create compressed archive for bytecode files
            const updatableArchive = path.join(compressedDir, 'OneWhispr-Scripts.7z');

            try {
              await runCommand(zipCommand, [
                'a', '-t7z', '-mx=9',
                updatableArchive,
                path.join(updatableDir, '*')
              ]);

              spinner.succeed(`✅ Bytecode files compressed`);

            } catch (error) {
              spinner.warn('⚠️ Failed to compress bytecode files, copying uncompressed');
              copyFolderRecursiveSync(updatableDir, compressedDir);
            }
          } else {
            spinner.warn('⚠️ 7zip not found - copying uncompressed bytecode files');
            spinner.info('💡 Install 7zip to enable compression');
            copyFolderRecursiveSync(updatableDir, compressedDir);
          }

          // Create final output structure
          if (!fs.existsSync(finalOutputDir)) {
            fs.mkdirSync(finalOutputDir, { recursive: true });
          }

          // Copy only the 7z files directly to the final output directory
          const baseRuntimeArchive = path.join(compressedDir, 'OneWhispr-Runtime-Base.7z');
          const updatableArchive = path.join(compressedDir, 'OneWhispr-Scripts.7z');

          if (fs.existsSync(baseRuntimeArchive)) {
            fs.copyFileSync(baseRuntimeArchive, path.join(finalOutputDir, 'OneWhispr-Runtime-Base.7z'));
          }

          if (fs.existsSync(updatableArchive)) {
            fs.copyFileSync(updatableArchive, path.join(finalOutputDir, 'OneWhispr-Scripts.7z'));
          }

          const fileCount = countFiles(finalOutputDir);
          const directorySize = getDirSize(finalOutputDir);
          const sizeMB = Math.round(directorySize / (1024 * 1024));

          spinner.succeed(`✅ Separated build created (${fileCount} files, ${sizeMB}MB)`);
          spinner.info(`📦 Base runtime: ${path.join(finalOutputDir, 'OneWhispr-Runtime-Base.7z')}`);
          spinner.info(`🔐 Bytecode scripts: ${path.join(finalOutputDir, 'OneWhispr-Scripts.7z')}`);
          spinner.info(`🚀 Ready for deployment - base runtime + protected bytecode scripts`);

          // Clean up temporary directories
          if (!options.debug) {
            spinner.text = '🧹 Cleaning up build files...';
            fs.rmSync(outputDir, { recursive: true, force: true });
            fs.rmSync(baseRuntimeDir, { recursive: true, force: true });
            fs.rmSync(updatableDir, { recursive: true, force: true });
            fs.rmSync(compressedDir, { recursive: true, force: true });
          }

          spinner.succeed('✅ Separated build completed successfully!');
          return finalOutputDir;
      } catch (err: any) {
        spinner.fail(`❌ Could not copy executable: ${err.message}`);
        console.error('Build error details:', err);
        throw err;
      }
    } else {
      spinner.fail(`❌ PyInstaller failed with code ${exitCode}`);
      console.error('Error output:');
      console.error(stderr);
      throw new Error(`PyInstaller failed with code ${exitCode}`);
    }
  } catch (err: any) {
    spinner.fail(`❌ Build failed: ${err.message}`);
    console.error('Build error details:', err);
    throw err;
  }
}

/**
 * Compile and package bytecode for quick updates
 */
async function compileAndPackageBytecode(
  pythonExePath: string,
  updatableDir: string,
  compressedDir: string,
  finalOutputDir: string,
  spinner: any
): Promise<void> {
  // Fix the project root calculation - we're in python/utils/, need to go up to one.whispr-app
  const projectRoot = path.resolve(__dirname, '../..');

  spinner.text = '🔐 Compiling Python files to bytecode...';

  // Compile Python files to .pyc bytecode for protection
  const mainPySource = path.join(projectRoot, 'python', 'main.py');
  const whisprSource = path.join(projectRoot, 'python', 'whispr');

  try {
    // Compile main.py to bytecode
    if (fs.existsSync(mainPySource)) {
      await runCommand(pythonExePath, [
        '-m', 'py_compile', mainPySource
      ]);

      // Find the generated .pyc file and copy it
      const pycacheDir = path.join(path.dirname(mainPySource), '__pycache__');

      if (fs.existsSync(pycacheDir)) {
        const pycFiles = fs.readdirSync(pycacheDir).filter(f => f.startsWith('main.') && f.endsWith('.pyc'));

        if (pycFiles.length > 0) {
          const pycFile = path.join(pycacheDir, pycFiles[0]);
          fs.copyFileSync(pycFile, path.join(updatableDir, 'main.pyc'));
          spinner.info(`✅ Compiled main.py to bytecode`);
        }
      }
    }

    // Compile entire whispr directory to bytecode with parallel processing
    if (fs.existsSync(whisprSource)) {
      console.log(`[DEBUG] Compiling whispr directory: ${whisprSource}`);
      await runCommand(pythonExePath, [
        '-m', 'compileall', '-b', '-j', '4', whisprSource  // Use 4 parallel workers
      ]);

      // Create whispr directory in updatable folder and copy .pyc files there
      const whisprDestDir = path.join(updatableDir, 'whispr');
      console.log(`[DEBUG] Creating whispr destination: ${whisprDestDir}`);

      if (!fs.existsSync(whisprDestDir)) {
        fs.mkdirSync(whisprDestDir, { recursive: true });
      }

      console.log(`[DEBUG] Copying .pyc files from ${whisprSource} to ${whisprDestDir}`);
      copyPycFiles(whisprSource, whisprDestDir);

      // Check what was actually copied
      const copiedFiles = fs.readdirSync(whisprDestDir, { recursive: true });
      console.log(`[DEBUG] Files copied to whispr destination: ${copiedFiles.length} files`);

      spinner.info(`✅ Compiled whispr/ directory to bytecode`);
    } else {
      console.warn(`[DEBUG] whispr directory not found: ${whisprSource}`);
    }

  } catch (error) {
    spinner.warn('⚠️ Bytecode compilation failed, copying source files');
    console.warn('Compilation error:', error);

    // Fallback: copy source files
    if (fs.existsSync(mainPySource)) {
      fs.copyFileSync(mainPySource, path.join(updatableDir, 'main.py'));
    }
    if (fs.existsSync(whisprSource)) {
      copyFolderRecursiveSync(whisprSource, updatableDir);
    }
  } finally {
    // Clean up .pyc files and __pycache__ directories from source
    cleanupPycFiles(projectRoot);
  }

  // Debug: Check what's in the updatable directory before compression
  console.log(`[DEBUG] Contents of updatable directory before compression:`);
  if (fs.existsSync(updatableDir)) {
    const files = fs.readdirSync(updatableDir, { recursive: true });
    files.forEach(file => {
      const filePath = path.join(updatableDir, file.toString());
      const stats = fs.statSync(filePath);
      console.log(`[DEBUG]   ${file} (${stats.size} bytes)`);
    });
  } else {
    console.log(`[DEBUG]   Directory does not exist: ${updatableDir}`);
  }

  // Create scripts version file for quick update
  spinner.text = '📝 Creating scripts version file...';
  const currentVersion = getVersion();
  const currentDate = new Date().toISOString();

  const scriptsVersionPath = path.join(updatableDir, 'scripts-version.json');
  const scriptsVersion = {
    version: currentVersion,
    releaseDate: currentDate,
    releaseNotes: 'Quick update - local build scripts',
    downloadUrl: 'local-build',
    updateType: 'scripts',
    compressionType: '7z',
    source: 'local-build'
  };
  fs.writeFileSync(scriptsVersionPath, JSON.stringify(scriptsVersion, null, 2));
  spinner.succeed(`✅ Scripts version file created (v${currentVersion})`);

  spinner.text = '🗜️ Compressing bytecode files...';

  // Get 7zip command from npm package
  const zipCommand = get7zipCommand();

  if (zipCommand) {
    // Create compressed archive for bytecode files
    const updatableArchive = path.join(compressedDir, 'OneWhispr-Scripts.7z');

    try {
      await runCommand(zipCommand, [
        'a', '-t7z', '-mx=9',
        updatableArchive,
        path.join(updatableDir, '*')
      ]);

      spinner.succeed(`✅ Bytecode files compressed`);

    } catch (error) {
      spinner.warn('⚠️ Failed to compress bytecode files, copying uncompressed');
      copyFolderRecursiveSync(updatableDir, compressedDir);
    }
  } else {
    spinner.warn('⚠️ 7zip not found - copying uncompressed bytecode files');
    spinner.info('💡 Install 7zip to enable compression');
    copyFolderRecursiveSync(updatableDir, compressedDir);
  }

  // Ensure final output directory exists
  if (!fs.existsSync(finalOutputDir)) {
    fs.mkdirSync(finalOutputDir, { recursive: true });
  }

  // For quick updates: clean up old Python files but preserve base runtime
  if (fs.existsSync(finalOutputDir)) {
    const items = fs.readdirSync(finalOutputDir);

    spinner.text = '🧹 Cleaning old Python files (preserving base runtime)...';

    for (const item of items) {
      const itemPath = path.join(finalOutputDir, item);

      // Keep these essential base runtime files
      if (item === '_internal' ||
          item === 'One Whispr Backend.exe' ||
          item === 'OneWhispr-Runtime-Base.7z') {
        spinner.info(`✅ Preserved: ${item}`);
        continue; // Preserve base runtime
      }

      // Remove everything else (old Python files, old archives, etc.)
      try {
        const stats = fs.statSync(itemPath);
        if (stats.isDirectory()) {
          fs.rmSync(itemPath, { recursive: true, force: true });
          spinner.info(`🗑️ Removed directory: ${item}`);
        } else {
          fs.unlinkSync(itemPath);
          spinner.info(`🗑️ Removed file: ${item}`);
        }
      } catch (error) {
        console.warn(`Warning: Could not remove ${itemPath}:`, error);
      }
    }
  }

  // Copy the new scripts 7z file
  const updatableArchive = path.join(compressedDir, 'OneWhispr-Scripts.7z');

  if (fs.existsSync(updatableArchive)) {
    fs.copyFileSync(updatableArchive, path.join(finalOutputDir, 'OneWhispr-Scripts.7z'));

    // Get the actual size of the scripts archive
    const archiveSize = fs.statSync(updatableArchive).size;
    const archiveSizeKB = Math.round(archiveSize / 1024);

    spinner.info(`🔐 Bytecode scripts: ${path.join(finalOutputDir, 'OneWhispr-Scripts.7z')}`);
    spinner.info(`📦 Quick update completed (${archiveSizeKB}KB)`);
    spinner.info(`🚀 Ready for deployment - preserved base runtime, updated Python scripts`);
  } else {
    throw new Error('Failed to create scripts archive');
  }

  // Clean up temporary directories
  try {
    if (fs.existsSync(updatableDir)) {
      fs.rmSync(updatableDir, { recursive: true, force: true });
    }
    if (fs.existsSync(compressedDir)) {
      fs.rmSync(compressedDir, { recursive: true, force: true });
    }
  } catch (error) {
    console.warn('Warning: Failed to clean up temporary directories:', error);
  }
}

// Run the build if this script is executed directly
if (import.meta.url.endsWith(process.argv[1]) || import.meta.url.includes('python-compile.ts')) {
  const options = parseArgs();
  
  // Debug: Show what arguments were parsed
  console.log(`🔍 Full process.argv:`, process.argv);
  console.log(`🔍 Command line args: ${process.argv.slice(2).join(' ')}`);
  console.log(`🔍 Parsed options:`, options);
  
  // Show help if requested
  if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log(`
🚀 One.Whispr Python Backend Compiler

Usage: npm run python-compile [options]

Options:
  --debug, -d           Enable debug mode (preserve build files)
  --clean, -c           Clean previous build files (default: true)
  --no-clean            Disable cleaning of previous build files
  --quick-update, -q    Quick update mode (bytecode only, no PyInstaller)
  --help, -h            Show this help message

Build Mode:
  Separated Build:      Creates base runtime + protected bytecode files
                        - Base runtime: exe + _internal → 7zip compressed (~1.2GB)
                        - Updatable files: main.pyc + whispr/ → compiled bytecode in 7zip
                        - Enables quick Python-only updates without full rebuild
                        - Python source code is compiled to .pyc for protection

Examples:
  npm run python-compile                    # Separated build (default)
  npm run python-compile --debug           # Debug mode (preserve build files)
  npm run python-compile --no-clean        # Don't clean previous builds
`);
    process.exit(0);
  }
  
  if (options.debug) {
    console.log('🔧 Running in debug mode - build files will be preserved');
  }

  console.log('🔄 Separated build mode - creating base runtime + protected bytecode files');
  console.log('📦 This creates 7zip compressed base runtime + compiled Python bytecode updates');
  
  buildPythonExecutable(options).catch(err => {
    console.error('❌ Build failed:', err);
    process.exit(1);
  });
}

export { buildPythonExecutable };