import { useState, useEffect, useCallback } from 'react';

// Types
export interface DownloadProgress {
  file: string;
  totalFiles: number;
  currentFile: number;
  progress: number;
  totalProgress: number;
  speed: number;
  eta: number;
}

export interface BackendDownloadProgress {
  type: 'runtime' | 'scripts';
  progress: number;
  speed: number;
  eta: number;
  status: string;
}

export interface LaunchReadiness {
  mainAppReady: boolean;
  backendReady: boolean;
  allReady: boolean;
  mainAppNeeded: boolean;
  backendNeeded: boolean;
  reason: string;
}

export interface SetupState {
  // Overall readiness
  launchReady: LaunchReadiness | null;

  // Download state
  downloadNeeded: boolean;
  downloadReason: string;
  isDownloading: boolean;
  downloadProgress: DownloadProgress | null;
  downloadError: string | null;
  downloadComplete: boolean;

  // Backend download state
  backendDownloading: boolean;
  backendProgress: BackendDownloadProgress | null;
  backendError: string | null;
  backendComplete: boolean;

  // Main app state
  mainAppError: string | null;

  // Overall state
  currentPhase: 'checking' | 'downloading' | 'starting' | 'error';
}

/**
 * Hook for managing the setup process
 */
export const useSetup = () => {
  // Setup state
  const [state, setState] = useState<SetupState>({
    // Overall readiness
    launchReady: null,

    // Download state
    downloadNeeded: false,
    downloadReason: '',
    isDownloading: false,
    downloadProgress: null,
    downloadError: null,
    downloadComplete: false,

    // Backend download state
    backendDownloading: false,
    backendProgress: null,
    backendError: null,
    backendComplete: false,

    // Main app state
    mainAppError: null,

    // Overall state
    currentPhase: 'checking'
  });
  
  // Check launch readiness (both main app and backend)
  const checkLaunchReady = useCallback(async () => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }

      setState(prev => ({ ...prev, currentPhase: 'checking' }));

      const result = await window.electron.ipcRenderer.invoke('launcher:check-ready');

      setState(prev => ({
        ...prev,
        launchReady: result,
        downloadNeeded: result.mainAppNeeded || result.backendNeeded,
        downloadReason: result.reason,
        currentPhase: result.allReady ? 'starting' : 'downloading'
      }));

      return !result.allReady;
    } catch (error) {
      console.error('Failed to check launch readiness:', error);

      setState(prev => ({
        ...prev,
        downloadError: error instanceof Error ? error.message : String(error),
        currentPhase: 'error'
      }));

      return false;
    }
  }, []);
  
  // Start main app download
  const startMainAppDownload = useCallback(async () => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }

      setState(prev => ({
        ...prev,
        isDownloading: true,
        downloadError: null,
        currentPhase: 'downloading'
      }));

      const result = await window.electron.ipcRenderer.invoke('download:start');

      if (!result) {
        throw new Error('Main app download failed to start');
      }

      return true;
    } catch (error) {
      console.error('Failed to start main app download:', error);

      setState(prev => ({
        ...prev,
        isDownloading: false,
        downloadError: error instanceof Error ? error.message : String(error),
        currentPhase: 'error'
      }));

      return false;
    }
  }, []);

  // Start backend download
  const startBackendDownload = useCallback(async () => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }

      setState(prev => ({ ...prev, backendError: null }));
      
      const result = await window.electron.ipcRenderer.invoke('backend:download');
      
      if (!result) {
        setState(prev => ({ 
          ...prev, 
          backendError: 'Failed to start backend download' 
        }));
      }
    } catch (error) {
      console.error('[SETUP] Backend download error:', error);
      setState(prev => ({ 
        ...prev, 
        backendError: error instanceof Error ? error.message : 'Unknown error' 
      }));
    }
  }, []);
  
  // Cancel main app download
  const cancelMainAppDownload = useCallback(async () => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }

      const result = await window.electron.ipcRenderer.invoke('download:cancel');

      if (result) {
        setState(prev => ({
          ...prev,
          isDownloading: false
        }));
      }

      return result;
    } catch (error) {
      console.error('Failed to cancel main app download:', error);
      return false;
    }
  }, []);

  // Cancel backend download
  const cancelBackendDownload = useCallback(async () => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }

      const result = await window.electron.ipcRenderer.invoke('backend:cancel');
      
      if (result) {
        setState(prev => ({
          ...prev,
          backendDownloading: false,
          backendProgress: null
        }));
      }
    } catch (error) {
      console.error('[SETUP] Cancel backend download error:', error);
    }
  }, []);
  
  // Launch main app
  const launchMainApp = useCallback(async () => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }
      
      setState(prev => ({
        ...prev,
        currentPhase: 'starting',
        mainAppError: null
      }));
      
      const result = await window.electron.ipcRenderer.invoke('launcher:launch-main-app');
      
      if (!result) {
        throw new Error('Failed to launch main app');
      }
      
      return true;
    } catch (error) {
      console.error('Failed to launch main app:', error);
      
      setState(prev => ({
        ...prev,
        mainAppError: error instanceof Error ? error.message : String(error),
        currentPhase: 'error'
      }));
      
      return false;
    }
  }, []);
  
  // Exit launcher
  const exitLauncher = useCallback(async () => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }
      
      await window.electron.ipcRenderer.invoke('launcher:exit');
    } catch (error) {
      console.error('Failed to exit launcher:', error);
    }
  }, []);
  
  // Set up event listeners
  useEffect(() => {
    if (!window.electron) {
      console.error('Electron API not available');
      return;
    }
    
    // Main app download progress
    const removeDownloadProgressListener = window.electron.ipcRenderer.on(
      'download:progress',
      (data: DownloadProgress) => {
        setState(prev => ({
          ...prev,
          downloadProgress: data,
          isDownloading: true
        }));
      }
    );

    // Main app download complete
    const removeDownloadCompleteListener = window.electron.ipcRenderer.on(
      'download:complete',
      (_data: any) => {
        setState(prev => ({
          ...prev,
          downloadComplete: true,
          isDownloading: false
        }));
      }
    );

    // Main app download error
    const removeDownloadErrorListener = window.electron.ipcRenderer.on(
      'download:error',
      (data: any) => {
        setState(prev => ({
          ...prev,
          downloadError: data.message || 'Unknown download error',
          isDownloading: false,
          currentPhase: 'error'
        }));
      }
    );

    // Backend download progress
    const removeBackendProgressListener = window.electron.ipcRenderer.on(
      'backend:progress',
      (data: BackendDownloadProgress) => {
        setState(prev => ({
          ...prev,
          backendProgress: data,
          backendDownloading: true
        }));
      }
    );

    // Backend download complete
    const removeBackendCompleteListener = window.electron.ipcRenderer.on(
      'backend:complete',
      () => {
        setState(prev => ({
          ...prev,
          backendDownloading: false,
          backendComplete: true,
          backendProgress: null
        }));
      }
    );

    // Backend download error
    const removeBackendErrorListener = window.electron.ipcRenderer.on(
      'backend:error',
      (data: { message: string }) => {
        setState(prev => ({
          ...prev,
          backendDownloading: false,
          backendError: data.message,
          backendProgress: null
        }));
      }
    );
    
    // Main app ready
    const removeMainAppReadyListener = window.electron.ipcRenderer.on(
      'launcher:main-app-ready',
      (_data: any) => {
        setState(prev => ({
          ...prev,
          mainAppReady: true,
          currentPhase: 'starting'
        }));
      }
    );
    
    // Main app error
    const removeMainAppErrorListener = window.electron.ipcRenderer.on(
      'launcher:main-app-error',
      (data: any) => {
        setState(prev => ({
          ...prev,
          mainAppError: data.error || 'Unknown main app error',
          currentPhase: 'error'
        }));
      }
    );
    
    // Clean up listeners
    return () => {
      removeDownloadProgressListener();
      removeDownloadCompleteListener();
      removeDownloadErrorListener();
      removeBackendProgressListener();
      removeBackendCompleteListener();
      removeBackendErrorListener();
      removeMainAppReadyListener();
      removeMainAppErrorListener();
    };
  }, []);
  
  // Auto-start the setup process
  useEffect(() => {
    console.log('[SETUP] useEffect triggered - starting setup process');

    const startSetup = async () => {
      try {
        // Get environment variables from main process
        if (!window.electron) {
          throw new Error('Electron API not available');
        }

        const env = await window.electron.ipcRenderer.invoke('launcher:get-env');
        const isDev = env.NODE_ENV === 'development';
        const forceDownload = env.FORCE_DOWNLOAD === 'true';
        console.log('[SETUP] Environment check - isDev:', isDev, 'forceDownload:', forceDownload);
        console.log('[SETUP] Environment variables:', env);

        if (isDev && !forceDownload) {
          // In dev mode without force download, skip download check and launch directly
          console.log('[SETUP] Development mode - launching directly');
          await launchMainApp();
          return;
        }

        // Production mode or forced download mode - check for downloads first
        console.log('[SETUP] Production/forced download mode - checking launch readiness');

        // Get the readiness result directly from the IPC call
        if (!window.electron) {
          throw new Error('Electron API not available');
        }

        const readiness = await window.electron.ipcRenderer.invoke('launcher:check-ready');
        console.log('[SETUP] Readiness result:', readiness);

        // Update state with the readiness info
        setState(prev => ({
          ...prev,
          launchReady: readiness,
          downloadNeeded: readiness.mainAppNeeded || readiness.backendNeeded,
          downloadReason: readiness.reason,
          currentPhase: readiness.allReady ? 'starting' : 'downloading'
        }));

        const needsDownload = !readiness.allReady;
        console.log('[SETUP] Needs download:', needsDownload);

        if (needsDownload) {
          // Start downloads based on what's needed
          console.log('[SETUP] Starting downloads - mainApp:', readiness.mainAppNeeded, 'backend:', readiness.backendNeeded);

          if (readiness.mainAppNeeded) {
            console.log('[SETUP] Starting main app download');
            await startMainAppDownload();
          }
          if (readiness.backendNeeded) {
            console.log('[SETUP] Starting backend download');
            await startBackendDownload();
          }
        } else {
          console.log('[SETUP] No downloads needed - launching main app');
          await launchMainApp();
        }
      } catch (error) {
        console.error('[SETUP] Error in startSetup:', error);
      }
    };

    startSetup();
  }, []); // Empty dependency array - only run once on mount

  // Auto-launch when all downloads are complete
  useEffect(() => {
    const checkAutoLaunch = async () => {
      // Only auto-launch if we're not in development mode (unless force download is enabled)
      const isDev = process.env.NODE_ENV === 'development';
      const forceDownload = process.env.FORCE_DOWNLOAD === 'true';
      if (isDev && !forceDownload) return;

      // Check if all required downloads are complete
      const mainAppDone = !state.launchReady?.mainAppNeeded || state.downloadComplete;
      const backendDone = !state.launchReady?.backendNeeded || state.backendComplete;

      if (mainAppDone && backendDone && state.currentPhase === 'downloading') {
        console.log('[SETUP] All downloads complete - launching main app');
        setState(prev => ({ ...prev, currentPhase: 'starting' }));
        await launchMainApp();
      }
    };

    checkAutoLaunch();
  }, [state.downloadComplete, state.backendComplete, state.launchReady, state.currentPhase]);
  
  return {
    state,
    actions: {
      checkLaunchReady,
      startMainAppDownload,
      startBackendDownload,
      cancelMainAppDownload,
      cancelBackendDownload,
      launchMainApp,
      exitLauncher
    },
    backendDownloading: state.backendDownloading,
    backendProgress: state.backendProgress,
    backendError: state.backendError,
    backendComplete: state.backendComplete,
  };
};
