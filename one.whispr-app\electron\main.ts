
import { app } from 'electron';
import { resetStore, createSettingsWindow, showSettingsWindow } from './windows';
import { resetDatabase } from './database/core/connection';
import { initializeFeatures, setupInitialDatabaseSync, setupBackendEventForwarding } from './features';
import { setupTray } from './tray';
import { backendEvents } from '../python/websocket';

const RESET_ON_START = true;

// Request a single instance lock to prevent multiple instances
const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
  // Quit if we're the second instance
  app.quit();
} else {
  app.whenReady().then(async () => {
    if (RESET_ON_START) {
      resetStore();
      resetDatabase();
    }

    // setup tray
    setupTray();

    // 1. Initialize all application features (database, backend, IPC)
    await initializeFeatures();
    console.log('[MAIN] Features initialized successfully');

    // 2. Create the settings window but don't show it yet
    createSettingsWindow();
    console.log('[MAIN] Settings window created');

    // 3. Set up backend event forwarding to the renderer now that window exists
    setupBackendEventForwarding();
    console.log('[MAIN] Backend event forwarding set up');

    // 4. Wait for initial database sync to complete
    await setupInitialDatabaseSync();
    console.log('[MAIN] Initial database sync completed');

    // 5. Wait for ML library initialization before showing the settings window
    console.log('[MAIN] Waiting for ML library initialization...');
    let settingsWindowShown = false;

    const showSettingsWindowOnce = () => {
      if (!settingsWindowShown) {
        settingsWindowShown = true;
        console.log('[MAIN] Showing settings window');
        showSettingsWindow();
      }
    };

    backendEvents.once('ml_libraries_initialized', () => {
      console.log('[MAIN] ML libraries initialized, showing settings window');
      showSettingsWindowOnce();
    });

    // Fallback timeout in case the ML initialization message is missed
    setTimeout(() => {
      if (!settingsWindowShown) {
        console.log('[MAIN] Timeout waiting for ML library initialization, showing settings window anyway');
        showSettingsWindowOnce();
      }
    }, 30000); // 30 second timeout
  });
}
