{"appId": "com.whispr.onewhispr", "productName": "One Whispr", "directories": {"output": ".release-microsoft"}, "files": [".dist/main/**/*", ".dist/renderer/**/*", "resources/**/*", "!resources/backend/*.7z", "node_modules/**/*", "!node_modules/**/test/**/*", "!node_modules/**/tests/**/*", "!node_modules/**/*.md", "!node_modules/**/*.txt", "!node_modules/**/LICENSE*", "!node_modules/**/CHANGELOG*", "!node_modules/**/README*", "!node_modules/**/.git*", "!node_modules/**/docs/**/*", "!node_modules/**/example/**/*", "!node_modules/**/examples/**/*"], "extraResources": [{"from": "resources/backend", "to": "backend", "filter": ["**/*"]}], "win": {"target": [{"target": "appx", "arch": ["x64"]}], "icon": "resources/icons/icon.ico", "publisherName": "CN=YourPublisherName", "identityName": "YourCompany.OneWhispr", "applicationId": "OneWhispr"}, "appx": {"displayName": "One Whispr", "publisherDisplayName": "Your Company Name", "identityName": "YourCompany.OneWhispr", "publisher": "CN=YourPublisherName", "applicationId": "OneWhispr", "backgroundColor": "#1a1a1a", "showNameOnTiles": true, "languages": ["en-US"], "setBuildNumber": true}, "publish": null, "buildDependenciesFromSource": false, "nodeGypRebuild": false, "buildVersion": "1.0.0"}