<?xml version="1.0" encoding="utf-8"?>
<!--suppress XmlUnusedNamespaceDeclaration -->
<Package
   xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10"
   xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10"
   xmlns:desktop="http://schemas.microsoft.com/appx/manifest/desktop/windows10"
   xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities">
  <!-- use single quotes to avoid double quotes escaping in the publisher value  -->
  <Identity Name="IjazSadiqBasha.OneWhispr"
    ProcessorArchitecture="x64"
    Publisher='CN=IjazSadiqBasha'
    Version="*******" />
  <Properties>
    <DisplayName>One Whispr</DisplayName>
    <PublisherDisplayName>One Whispr Team</PublisherDisplayName>
    <Description>One Whispr - AI-powered transcription and productivity app</Description>
    <Logo>assets\StoreLogo.png</Logo>
  </Properties>
  <Resources>
    <Resource Language="en-US" />
  </Resources>
  <Dependencies>
    <TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.14316.0" MaxVersionTested="10.0.14316.0" />
  </Dependencies>
  <Capabilities>
    <rescap:Capability Name="runFullTrust"/>
  </Capabilities>
  <Applications>
    <Application Id="OneWhispr" Executable="app\One Whispr.exe" EntryPoint="Windows.FullTrustApplication">
      <uap:VisualElements
       BackgroundColor="#1a1a1a"
       DisplayName="One Whispr"
       Square150x150Logo="assets\Square150x150Logo.png"
       Square44x44Logo="assets\Square44x44Logo.png"
       Description="One Whispr - AI-powered transcription and productivity app">
        
        <uap:DefaultTile Wide310x150Logo="assets\Wide310x150Logo.png" > <uap:ShowNameOnTiles> <uap:ShowOn Tile="wide310x150Logo" /> <uap:ShowOn Tile="square150x150Logo" /> </uap:ShowNameOnTiles> </uap:DefaultTile>
        
      </uap:VisualElements>
      
    </Application>
  </Applications>
</Package>
