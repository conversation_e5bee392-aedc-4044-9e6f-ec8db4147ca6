import { app, ipcMain } from 'electron';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as crypto from 'crypto';
import axios from 'axios';
import { getLauncherWindow } from '../managers/window';
import { MAIN_APP_UPDATES } from '../../src/lib/constants';

// Types
interface DownloadManifest {
  version: string;
  files: ManifestFile[];
  totalSize: number;
}

interface ManifestFile {
  path: string;
  size: number;
  checksum: string;
  url: string;
}

interface DownloadProgress {
  file: string;
  totalFiles: number;
  currentFile: number;
  progress: number;
  totalProgress: number;
  speed: number;
  eta: number;
}

/**
 * Simplified downloader that handles all download-related operations
 */
class Downloader {
  private manifest: DownloadManifest | null = null;
  private downloadPath: string;
  private manifestUrl: string;
  private isDownloading: boolean = false;
  private abortController: AbortController | null = null;
  
  constructor() {
    // Set download path - use custom path if specified in environment, otherwise use userData/MainApp
    if (process.env.DOWNLOAD_PATH) {
      // Use custom download path (relative to process.cwd())
      this.downloadPath = path.resolve(process.cwd(), process.env.DOWNLOAD_PATH);
      console.log(`[DOWNLOADER] Using custom download path: ${this.downloadPath}`);
    } else {
      // Default to userData/MainApp
      this.downloadPath = path.join(app.getPath('userData'), 'MainApp');
      console.log(`[DOWNLOADER] Using default download path: ${this.downloadPath}`);
    }

    // Set manifest URL from constants
    this.manifestUrl = MAIN_APP_UPDATES.manifestUrl;

    // Create download directory if it doesn't exist
    fs.ensureDirSync(this.downloadPath);

    // Setup IPC handlers
    this.setupIpcHandlers();
  }
  
  /**
   * Setup IPC handlers for download-related events
   */
  private setupIpcHandlers(): void {
    // Start download
    ipcMain.handle('download:start', async () => {
      return this.startDownload();
    });
    
    // Cancel download
    ipcMain.handle('download:cancel', () => {
      return this.cancelDownload();
    });
    
    // Check if download is needed
    ipcMain.handle('download:check-needed', async () => {
      return this.checkDownloadNeeded();
    });
    
    // Get manifest
    ipcMain.handle('download:get-manifest', () => {
      return this.manifest;
    });
  }
  
  /**
   * Fetch server version info (contains releaseDate)
   */
  private async fetchServerVersion(): Promise<any | null> {
    try {
      const versionUrl = MAIN_APP_UPDATES.versionUrl;
      console.log(`[DOWNLOADER] Fetching server version from ${versionUrl}`);

      const response = await axios.get(versionUrl, {
        timeout: 30000,
        headers: {
          'User-Agent': 'OneWhispr-Setup/1.0.0'
        }
      });

      if (response.status !== 200) {
        throw new Error(`Failed to fetch version: ${response.status}`);
      }

      return response.data;
    } catch (error) {
      console.error('[DOWNLOADER] Error fetching server version:', error);
      return null;
    }
  }

  /**
   * Fetch the download manifest
   */
  private async fetchManifest(): Promise<DownloadManifest | null> {
    try {
      console.log(`[DOWNLOADER] Fetching manifest from ${this.manifestUrl}`);

      const response = await axios.get(this.manifestUrl, {
        timeout: 30000, // 30 second timeout
        headers: {
          'User-Agent': 'OneWhispr-Setup/1.0.0'
        }
      });

      if (response.status !== 200) {
        throw new Error(`Failed to fetch manifest: ${response.status}`);
      }

      const responseData = response.data;

      // Log the actual response for debugging
      console.log('[DOWNLOADER] Received response type:', typeof responseData);

      // Check if we got a string (directory listing) instead of JSON
      if (typeof responseData === 'string') {
        console.log('[DOWNLOADER] Response preview:', responseData.substring(0, 200) + '...');
        console.error('[DOWNLOADER] Received directory listing instead of JSON manifest');
        throw new Error('Server returned directory listing instead of manifest JSON');
      }

      const manifest = responseData as DownloadManifest;
      console.log('[DOWNLOADER] Response preview:', JSON.stringify(manifest, null, 2).substring(0, 200) + '...');

      // Validate manifest structure
      if (!manifest.version || !manifest.files || !Array.isArray(manifest.files)) {
        console.error('[DOWNLOADER] Invalid manifest structure. Expected: {version: string, files: array}');
        console.error('[DOWNLOADER] Received:', manifest);
        throw new Error('Invalid manifest structure');
      }

      return manifest;
    } catch (error) {
      console.error('[DOWNLOADER] Error fetching manifest:', error);

      // In development mode with custom download path, provide a helpful message
      if (process.env.DOWNLOAD_PATH) {
        console.log('[DOWNLOADER] Development mode detected - manifest server may not be properly configured');
        console.log('[DOWNLOADER] This is expected during development testing');
      }

      return null;
    }
  }
  
  /**
   * Check if download is needed by comparing release dates with server
   */
  public async checkDownloadNeeded(): Promise<{ needed: boolean, reason: string }> {
    try {
      // Fetch server version info (contains releaseDate)
      const serverVersion = await this.fetchServerVersion();

      if (!serverVersion) {
        return { needed: false, reason: 'Failed to fetch server version - will try to launch existing app' };
      }

      // Check if local version file exists
      const versionPath = path.join(this.downloadPath, 'version.json');

      if (!fs.existsSync(versionPath)) {
        return { needed: true, reason: 'No version file found - first time installation' };
      }

      // Read the installed version
      const localVersion = await fs.readJson(versionPath);

      // Check for rollback flag (forces update regardless of date)
      if (serverVersion.isRollback === true) {
        return { needed: true, reason: 'Rollback update required' };
      }

      // Compare release dates (like Python components)
      if (localVersion.releaseDate && serverVersion.releaseDate) {
        const localDate = new Date(localVersion.releaseDate);
        const serverDate = new Date(serverVersion.releaseDate);

        if (serverDate > localDate) {
          return { needed: true, reason: `New release available: ${serverVersion.releaseDate}` };
        }

        // Release dates match - no update needed
        return { needed: false, reason: 'All files up to date' };
      }

      // Fallback to version comparison if no releaseDate
      if (localVersion.version !== serverVersion.version) {
        return { needed: true, reason: `New version available: ${serverVersion.version}` };
      }

      // If we reach here, versions match - no need for file-by-file checking
      return { needed: false, reason: 'All files up to date' };
    } catch (error) {
      console.error('[DOWNLOADER] Error checking download needed:', error);
      return { needed: false, reason: 'Error checking files - will try to launch existing app' };
    }
  }
  
  /**
   * Start downloading files from the manifest
   */
  public async startDownload(): Promise<boolean> {
    if (this.isDownloading) {
      console.log('[DOWNLOADER] Download already in progress');
      return false;
    }
    
    try {
      this.isDownloading = true;
      this.abortController = new AbortController();
      
      // Fetch manifest if not already fetched
      if (!this.manifest) {
        this.manifest = await this.fetchManifest();
        
        if (!this.manifest) {
          throw new Error('Failed to fetch manifest');
        }
      }
      
      const totalFiles = this.manifest.files.length;
      
      // Download each file
      for (let i = 0; i < totalFiles; i++) {
        const file = this.manifest.files[i];

        // Handle ASAR files specially - download as .tmp and rename after verification
        const isAsarFile = file.path.endsWith('.asar');

        // Use proper directory structure for all files
        const properPath = file.path.replace(/^\.\//, '');
        const finalPath = path.join(this.downloadPath, properPath);
        const downloadPath = isAsarFile ? finalPath + '.tmp' : finalPath;

        // Ensure directory exists
        await fs.ensureDir(path.dirname(downloadPath));

        // Download the file (to .tmp for ASAR files)
        await this.downloadFile(file, i + 1, totalFiles, downloadPath);

        // Verify checksum
        const isValid = await this.verifyChecksum(downloadPath, file.checksum);

        if (!isValid) {
          throw new Error(`Checksum verification failed for ${file.path}`);
        }

        // Rename ASAR files from .tmp to final name after successful verification
        if (isAsarFile) {
          // Use Node.js fs.rename instead of fs-extra's move to avoid package validation
          await new Promise<void>((resolve, reject) => {
            require('fs').rename(downloadPath, finalPath, (err: any) => {
              if (err) reject(err);
              else resolve();
            });
          });
          console.log(`[DOWNLOADER] Renamed ${downloadPath} to ${finalPath}`);
        }
      }
      
      // Fetch server version to get releaseDate
      const serverVersion = await this.fetchServerVersion();

      // Save version information with releaseDate
      await fs.writeJson(path.join(this.downloadPath, 'version.json'), {
        version: this.manifest.version,
        releaseDate: serverVersion?.releaseDate,
        installedAt: new Date().toISOString()
      });

      this.isDownloading = false;
      this.abortController = null;
      
      // Notify renderer that download is complete
      const launcherWindow = getLauncherWindow();
      if (launcherWindow) {
        launcherWindow.webContents.send('download:complete', {
          version: this.manifest.version
        });
      }
      
      return true;
    } catch (error) {
      console.error('[DOWNLOADER] Download error:', error);
      
      this.isDownloading = false;
      this.abortController = null;
      
      // Notify renderer of error
      const launcherWindow = getLauncherWindow();
      if (launcherWindow) {
        launcherWindow.webContents.send('download:error', {
          message: error instanceof Error ? error.message : String(error)
        });
      }
      
      return false;
    }
  }
  
  /**
   * Download a single file with progress reporting
   */
  private async downloadFile(file: ManifestFile, fileIndex: number, totalFiles: number, customFilePath?: string): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        console.log(`[DOWNLOADER] Downloading ${file.path}`);
        
        // Use custom file path if provided, otherwise use proper directory structure
        const filePath = customFilePath || (() => {
          const properPath = file.path.replace(/^\.\//, '');
          return path.join(this.downloadPath, properPath);
        })();
        const fileDir = path.dirname(filePath);
        
        // Ensure directory exists
        await fs.ensureDir(fileDir);
        
        // Create write stream
        const writer = fs.createWriteStream(filePath);
        
        // Track progress
        let downloadedBytes = 0;
        const startTime = Date.now();
        let lastUpdateTime = startTime;
        let lastBytes = 0;
        let speed = 0;
        
        // Calculate timeout based on file size (minimum 60s, +30s per 50MB)
        const timeoutMs = Math.max(60000, 60000 + Math.floor(file.size / (50 * 1024 * 1024)) * 30000);
        console.log(`[DOWNLOADER] File ${file.path} (${(file.size / 1024 / 1024).toFixed(1)}MB) - timeout: ${timeoutMs / 1000}s`);

        // Download the file
        const response = await axios({
          method: 'get',
          url: file.url,
          responseType: 'stream',
          signal: this.abortController?.signal,
          timeout: timeoutMs,
          headers: {
            'User-Agent': 'OneWhispr-Setup/1.0.0'
          }
        });
        
        response.data.on('data', (chunk: Buffer) => {
          downloadedBytes += chunk.length;
          
          // Calculate progress
          const progress = Math.min(100, Math.round((downloadedBytes / file.size) * 100));
          const totalProgress = Math.min(100, Math.round(
            ((fileIndex - 1) / totalFiles * 100) + (progress / totalFiles)
          ));
          
          // Calculate speed and ETA
          const now = Date.now();
          const timeDiff = (now - lastUpdateTime) / 1000; // in seconds
          
          if (timeDiff >= 0.5) { // Update every 500ms
            speed = Math.round((downloadedBytes - lastBytes) / timeDiff);
            lastBytes = downloadedBytes;
            lastUpdateTime = now;
          }
          
          // Calculate ETA
          const remainingBytes = file.size - downloadedBytes;
          const eta = speed > 0 ? Math.round(remainingBytes / speed) : 0;
          
          // Send progress update
          const launcherWindow = getLauncherWindow();
          if (launcherWindow) {
            launcherWindow.webContents.send('download:progress', {
              file: file.path,
              totalFiles,
              currentFile: fileIndex,
              progress,
              totalProgress,
              speed,
              eta
            } as DownloadProgress);
          }
        });
        
        // Handle completion
        response.data.pipe(writer);
        
        writer.on('finish', () => {
          console.log(`[DOWNLOADER] Successfully downloaded ${file.path}`);
          resolve();
        });
        writer.on('error', (error) => {
          console.error(`[DOWNLOADER] Write error for ${file.path}:`, error);
          reject(error);
        });

        response.data.on('error', (error: any) => {
          console.error(`[DOWNLOADER] Stream error for ${file.path}:`, error);
          reject(error);
        });
      } catch (error) {
        console.error(`[DOWNLOADER] Request error for ${file.path}:`, error);
        reject(error);
      }
    });
  }
  
  /**
   * Verify file checksum
   */
  private async verifyChecksum(filePath: string, expectedChecksum: string): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        const hash = crypto.createHash('sha256');
        const stream = fs.createReadStream(filePath);
        
        stream.on('data', (data) => {
          hash.update(data);
        });
        
        stream.on('end', () => {
          const fileChecksum = hash.digest('hex');
          const isValid = fileChecksum === expectedChecksum;
          
          if (!isValid) {
            console.error(`[DOWNLOADER] Checksum mismatch for ${filePath}`);
            console.error(`Expected: ${expectedChecksum}`);
            console.error(`Got: ${fileChecksum}`);
          }
          
          resolve(isValid);
        });
        
        stream.on('error', (error) => {
          console.error(`[DOWNLOADER] Error reading file for checksum: ${error}`);
          resolve(false);
        });
      } catch (error) {
        console.error('[DOWNLOADER] Error verifying checksum:', error);
        resolve(false);
      }
    });
  }
  
  /**
   * Cancel the current download
   */
  public cancelDownload(): boolean {
    if (!this.isDownloading || !this.abortController) {
      return false;
    }
    
    console.log('[DOWNLOADER] Cancelling download');
    
    this.abortController.abort();
    this.isDownloading = false;
    this.abortController = null;
    
    return true;
  }
  
  /**
   * Get the download path
   */
  public getDownloadPath(): string {
    return this.downloadPath;
  }
  
  /**
   * Check if a download is in progress
   */
  public isDownloadInProgress(): boolean {
    return this.isDownloading;
  }
}

// Export singleton instance
export const downloader = new Downloader();
