import { app, ipcMain, shell } from 'electron';
import * as fs from 'fs-extra';
import * as path from 'path';
import { getLauncherWindow, closeLauncherWindow } from './window';
import { downloader } from '../services/main-app-downloader';
import { backendDownloader } from '../services/backend-downloader';
// Removed IS_DEV import - we'll use app.isPackaged directly

/**
 * Simplified launcher that handles main app launching
 */
class Launcher {
  private mainAppPath: string;
  private launchTimeout: NodeJS.Timeout | null = null;
  private isLaunching: boolean = false; // Add guard to prevent double launches
  
  constructor() {
    // Set main app path based on environment
    const forceDownload = process.env.FORCE_DOWNLOAD === 'true';

    if (!app.isPackaged && !forceDownload) {
      // In development mode without forced download, point directly to the built exe in win-unpacked
      this.mainAppPath = path.join(process.cwd(), '..', 'one.whispr-app', '.release', 'win-unpacked', 'One Whispr.exe');
      console.log('[LAUNCHER] Using development path:', this.mainAppPath);
    } else {
      // In production or forced download mode, use the downloaded files
      this.mainAppPath = path.join(downloader.getDownloadPath(), 'One Whispr.exe');
      console.log('[LAUNCHER] Using downloaded files path:', this.mainAppPath);
    }

    // Setup IPC handlers
    this.setupIpcHandlers();
  }
  
  /**
   * Setup IPC handlers for launcher-related events
   */
  private setupIpcHandlers(): void {
    // Remove existing handlers first to prevent duplicates during hot reload
    ipcMain.removeHandler('launcher:check-ready');
    ipcMain.removeHandler('launcher:launch-main-app');
    ipcMain.removeHandler('launcher:exit');

    // Check if everything is ready to launch
    ipcMain.handle('launcher:check-ready', async () => {
      return this.checkLaunchReady();
    });

    // Launch main app
    ipcMain.handle('launcher:launch-main-app', async () => {
      return this.launchMainApp();
    });

    // Get environment variables
    ipcMain.handle('launcher:get-env', async () => {
      return {
        NODE_ENV: process.env.NODE_ENV,
        FORCE_DOWNLOAD: process.env.FORCE_DOWNLOAD,
        DOWNLOAD_PATH: process.env.DOWNLOAD_PATH
      };
    });

    // Exit launcher
    ipcMain.handle('launcher:exit', () => {
      console.log('[LAUNCHER] Exit requested via IPC');
      
      // Close the launcher window properly (sets isQuitting flag)
      closeLauncherWindow();
      
      // Force quit after a moment if normal quit doesn't work
      setTimeout(() => {
        app.quit();
        setTimeout(() => {
          process.exit(0);
        }, 1000);
      }, 500);
      
      return true;
    });
    
    console.log('[LAUNCHER] IPC handlers registered');
  }

  /**
   * Check if everything is ready to launch (main app + backend)
   */
  public async checkLaunchReady(): Promise<{
    mainAppReady: boolean,
    backendReady: boolean,
    allReady: boolean,
    mainAppNeeded: boolean,
    backendNeeded: boolean,
    reason: string
  }> {
    try {
      // Check main app
      const mainAppReady = fs.existsSync(this.mainAppPath);
      console.log('[LAUNCHER] Main app path:', this.mainAppPath);
      console.log('[LAUNCHER] Main app ready:', mainAppReady);

      const mainAppCheck = await downloader.checkDownloadNeeded();
      console.log('[LAUNCHER] Main app check result:', mainAppCheck);

      // Check backend
      const backendReady = await backendDownloader.isBackendReady();
      console.log('[LAUNCHER] Backend ready:', backendReady);

      const backendCheck = await backendDownloader.checkBackendUpdate();
      console.log('[LAUNCHER] Backend check result:', backendCheck);

      const allReady = mainAppReady && backendReady && !mainAppCheck.needed &&
                      !backendCheck.runtimeNeeded && !backendCheck.scriptsNeeded;

      let reason = 'Ready to launch';
      if (!mainAppReady || mainAppCheck.needed) {
        reason = 'Main app needs download/update';
      } else if (!backendReady || backendCheck.runtimeNeeded || backendCheck.scriptsNeeded) {
        reason = backendCheck.reason;
      }

      const result = {
        mainAppReady,
        backendReady: backendReady,
        allReady,
        mainAppNeeded: mainAppCheck.needed,
        backendNeeded: backendCheck.runtimeNeeded || backendCheck.scriptsNeeded,
        reason
      };

      console.log('[LAUNCHER] Final readiness result:', result);
      return result;
    } catch (error) {
      console.error('[LAUNCHER] Error checking launch readiness:', error);
      return {
        mainAppReady: false,
        backendReady: false,
        allReady: false,
        mainAppNeeded: true,
        backendNeeded: true,
        reason: 'Error checking readiness'
      };
    }
  }

  /**
   * Launch the main app using Electron's shell API (like double-clicking)
   */
  public async launchMainApp(): Promise<boolean> {
    // Prevent double launches
    if (this.isLaunching) {
      console.log('[LAUNCHER] Launch already in progress, ignoring duplicate request');
      return true;
    }

    try {
      this.isLaunching = true;
      console.log('[LAUNCHER] Attempting to launch main app...');
      console.log('[LAUNCHER] Main app path:', this.mainAppPath);
      console.log('[LAUNCHER] Path exists:', fs.existsSync(this.mainAppPath));
      
      // Check if the main app exists
      if (!fs.existsSync(this.mainAppPath)) {
        console.error('[LAUNCHER] Main app not found:', this.mainAppPath);
        
        // Notify renderer
        const launcherWindow = getLauncherWindow();
        if (launcherWindow) {
          launcherWindow.webContents.send('launcher:main-app-error', {
            error: 'Main app not found',
            path: this.mainAppPath
          });
        }
        
        this.isLaunching = false;
        return false;
      }
      
      console.log('[LAUNCHER] Using Electron shell.openPath (equivalent to double-clicking)...');
      
      // Use shell.openPath - this is exactly like double-clicking the exe file
      const result = await shell.openPath(this.mainAppPath);
      
      if (result) {
        console.error('[LAUNCHER] shell.openPath failed:', result);
        throw new Error(`Failed to open: ${result}`);
      }
      
      console.log('[LAUNCHER] shell.openPath successful - exe should be running independently');
      this.isLaunching = false;

      // Wait for ML libraries initialization instead of immediate timeout
      console.log('[LAUNCHER] Waiting for main app to initialize ML libraries...');
      this.waitForMainAppReady();
      
      return true;
    } catch (error) {
      console.error('[LAUNCHER] Error launching main app:', error);
      this.isLaunching = false;
      
      // Notify renderer of error
      const launcherWindow = getLauncherWindow();
      if (launcherWindow) {
        launcherWindow.webContents.send('launcher:main-app-error', {
          error: 'Failed to launch main app',
          details: error instanceof Error ? error.message : String(error)
        });
      }
      
      return false;
    }
  }
  
  /**
   * Wait for main app to be ready (ML libraries initialized) before closing launcher
   */
  private waitForMainAppReady(): void {
    console.log('[LAUNCHER] Starting to wait for main app readiness...');

    // Set up a file watcher or polling mechanism to detect when main app is ready
    // We'll use a simple polling approach to check for a ready signal
    const checkInterval = 1000; // Check every second
    const maxWaitTime = 60000; // Maximum 60 seconds
    const startTime = Date.now();
    const readySignalPath = path.join(app.getPath('temp'), 'one-whispr-ready.signal');

    console.log('[LAUNCHER] Watching for ready signal at:', readySignalPath);

    // Clean up any existing signal file first
    try {
      if (fs.existsSync(readySignalPath)) {
        fs.unlinkSync(readySignalPath);
        console.log('[LAUNCHER] Cleaned up existing ready signal file');
      }
    } catch (error) {
      console.warn('[LAUNCHER] Failed to clean up existing ready signal file:', error);
    }

    const checkReady = () => {
      const elapsed = Date.now() - startTime;

      if (elapsed > maxWaitTime) {
        console.log('[LAUNCHER] Timeout waiting for main app readiness (60s), closing anyway');
        this.closeLauncher();
        return;
      }

      // Check if main app has created a ready signal file
      if (fs.existsSync(readySignalPath)) {
        console.log('[LAUNCHER] Main app ready signal detected after', Math.round(elapsed / 1000), 'seconds');

        // Clean up the signal file
        try {
          fs.unlinkSync(readySignalPath);
          console.log('[LAUNCHER] Cleaned up ready signal file');
        } catch (error) {
          console.warn('[LAUNCHER] Failed to clean up ready signal file:', error);
        }

        this.closeLauncher();
        return;
      }

      // Log progress every 10 seconds
      if (elapsed % 10000 < checkInterval) {
        console.log('[LAUNCHER] Still waiting for main app readiness...', Math.round(elapsed / 1000), 's elapsed');
      }

      // Continue checking
      this.launchTimeout = setTimeout(checkReady, checkInterval);
    };

    // Start checking
    checkReady();
  }

  /**
   * Close the launcher properly
   */
  private closeLauncher(): void {
    console.log('[LAUNCHER] Closing launcher after main app is ready');

    // Clear any pending timeout
    if (this.launchTimeout) {
      clearTimeout(this.launchTimeout);
      this.launchTimeout = null;
    }

    // Close the launcher window properly (sets isQuitting flag)
    closeLauncherWindow();

    // Force quit if the window close doesn't trigger app quit
    setTimeout(() => {
      console.log('[LAUNCHER] Force quitting if still running');
      app.quit();

      // If app.quit() doesn't work (common in dev mode), force exit
      setTimeout(() => {
        console.log('[LAUNCHER] Final force exit');
        process.exit(0);
      }, 2000);
    }, 1000);
  }

  /**
   * Re-initialize the launcher (useful for hot reload)
   */
  public reinitialize(): void {
    console.log('[LAUNCHER] Re-initializing launcher...');
    this.setupIpcHandlers();
  }

  /**
   * Clean up resources
   */
  public cleanup(): void {
    // Clear launch timeout
    if (this.launchTimeout) {
      clearTimeout(this.launchTimeout);
      this.launchTimeout = null;
    }
    
    // Reset launching flag
    this.isLaunching = false;
    
    // Remove IPC handlers
    ipcMain.removeHandler('launcher:check-ready');
    ipcMain.removeHandler('launcher:launch-main-app');
    ipcMain.removeHandler('launcher:get-env');
    ipcMain.removeHandler('launcher:exit');
  }
}

// Export singleton instance
export const launcher = new Launcher();
