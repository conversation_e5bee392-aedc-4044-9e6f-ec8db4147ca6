directories:
  output: .release-direct
  buildResources: build
appId: one.whispr-app
productName: One Whispr
asar: true
files:
  - filter:
      - .dist/main/**/*
      - .dist/renderer/**/*
      - node_modules/**/*
      - '!node_modules/.cache/**/*'
      - '!python/utils/**/*'
asarUnpack:
  - node_modules/**/*.node
extraResources:
  - from: python/models
    to: models
    filter:
      - '**/*'
  - from: .dist/One Whispr Backend
    to: backend
    filter:
      - '**/*'
  - from: src/assets/one.whispr-white.png
    to: one.whispr-white.png
  - from: src/assets/one.whispr-black.png
    to: one.whispr-black.png
  - from: src/assets/sounds
    to: sounds
win:
  target:
    - target: dir
      arch:
        - x64
  icon: src/assets/icon.ico
nsis: null
squirrelWindows: null
electronVersion: 35.0.0
