import '@src/main.css'
import React from 'react';
import ReactD<PERSON> from 'react-dom/client';
import { ThemeProvider } from '@src/components/layout/theme-provider';
import { useSetup } from '@src/hooks/useSetup';
import { Progress } from '@src/components/ui/progress';
import { formatBytes } from '@src/lib/utils';
import oneWhisprIcon from '@src/assets/one.whispr-icon.png';

const GlobalProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
      {children}
    </ThemeProvider>
  );
};

/**
 * Main application component
 */
const App: React.FC = () => {
  const { state } = useSetup();
  
  // Get status text based on current phase
  const getStatusText = () => {
    switch (state.currentPhase) {
      case 'checking':
        return 'Checking for updates...';
      case 'downloading':
        // Show specific download status
        if (state.backendDownloading && state.backendProgress) {
          // Show the actual status message from the backend (e.g., "Extracting CUDA runtime from local files...")
          return state.backendProgress.status || `Downloading Backend ${state.backendProgress.type === 'runtime' ? 'Runtime' : 'Scripts'}`;
        }
        if (state.isDownloading) {
          return 'Installing One Whispr';
        }
        return 'Downloading updates...';
      case 'starting':
        return 'Starting One Whispr...';
      case 'waiting':
        return 'Initializing AI models...';
      case 'error':
        return state.downloadError || state.backendError || state.mainAppError || 'An error occurred';
      default:
        return 'Starting...';
    }
  };
  
  // Determine if we should show progress
  const showProgress = state.currentPhase === 'downloading';

  // Get progress data (prioritize Python progress if active, otherwise main app)
  // Only show progress data during downloading phase
  const progress = (() => {
    if (state.currentPhase !== 'downloading') {
      return null;
    }

    if (state.backendDownloading && state.backendProgress) {
      return {
        overallProgress: state.backendProgress.progress || 0,
        filesCompleted: 1,
        totalFiles: 1,
        downloadSpeed: state.backendProgress.speed || 0,
        type: 'backend' as const
      };
    }

    if (state.downloadProgress) {
      return {
        overallProgress: state.downloadProgress.totalProgress || 0,
        filesCompleted: state.downloadProgress.currentFile || 0,
        totalFiles: state.downloadProgress.totalFiles || 0,
        downloadSpeed: state.downloadProgress.speed || 0,
        type: 'main' as const
      };
    }

    return null;
  })();
  
  // Get error message
  const error = state.downloadError || state.backendError || state.mainAppError;
  
  return (
    <div className="bg-sidebar h-screen w-full flex flex-col items-center justify-center overflow-hidden">
      {/* Logo centered vertically and horizontally */}
      <div className="flex flex-col items-center gap-8 mt-12">
        <img src={oneWhisprIcon} alt="One Whispr" className="size-14" />
        
        {/* Status text below logo */}
        <p className="text-foreground text-[16px] text-center">{getStatusText()}</p>

        {/* Progress area below status - fixed height to prevent layout shift */}
        <div className="flex flex-col items-center h-20 justify-center mt-4">
          {/* Progress bar - hidden during non-download phases but space reserved */}
          <div className="w-56 h-2 mb-3">
            {showProgress && progress && (
              <Progress value={progress.overallProgress} />
            )}
          </div>

          {/* Download counter and percentage - Discord style - below progress bar */}
          <div className="h-5 flex items-center justify-between w-56 mb-1">
            {progress && (
              <>
                <p className="text-xs text-muted-foreground">
                  {progress.type === 'backend'
                    ? (state.backendProgress?.type === 'runtime' ? 'CUDA drivers' : 'Python scripts')
                    : `${progress.filesCompleted} of ${progress.totalFiles} files`
                  }
                </p>
                <p className="text-xs text-muted-foreground font-medium">
                  {progress.overallProgress.toFixed(1)}%
                </p>
              </>
            )}
          </div>

          {/* Speed display - always reserve space to prevent layout shift */}
          <div className="h-4 flex items-center justify-center">
            {progress && progress.downloadSpeed > 0 && (
              <p className="text-xs text-muted-foreground">
                {formatBytes(progress.downloadSpeed)}/s
              </p>
            )}
          </div>
        </div>

        {/* Error message */}
        {error && (
          <div className="text-red-400 text-xs text-center max-w-56 px-2">
            {error}
          </div>
        )}
      </div>
    </div>
  );
};

// Initialize the application
const rootElement = document.getElementById("root");
if (rootElement) {
  const root = ReactDOM.createRoot(rootElement);
  root.render(
    <React.StrictMode>
      <GlobalProviders>
        <App />
      </GlobalProviders>
    </React.StrictMode>
  );
}