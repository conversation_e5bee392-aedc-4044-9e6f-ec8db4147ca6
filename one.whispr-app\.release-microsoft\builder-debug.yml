x64:
  firstOrDefaultFilePatterns:
    - '!build{,/**/*}'
    - .dist/main/**/*
    - .dist/renderer/**/*
    - '!**/node_modules/**'
    - node_modules/**/*
    - '!node_modules/.cache/**/*'
    - '!python/utils/**/*'
    - package.json
    - '!**/*.{iml,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,suo,xproj,cc,d.ts,mk,a,o,obj,forge-meta,pdb}'
    - '!**/._*'
    - '!**/electron-builder.{yaml,yml,json,json5,toml,ts}'
    - '!**/{.git,.hg,.svn,CVS,RCS,SCCS,__pycache__,.DS_Store,thumbs.db,.gitignore,.gitkeep,.gitattributes,.npmignore,.idea,.vs,.flowconfig,.jshintrc,.eslintrc,.circleci,.yarn-integrity,.yarn-metadata.json,yarn-error.log,yarn.lock,package-lock.json,npm-debug.log,pnpm-lock.yaml,appveyor.yml,.travis.yml,circle.yml,.nyc_output,.husky,.github,electron-builder.env}'
    - '!.yarn{,/**/*}'
    - '!.editorconfig'
    - '!.yarnrc.yml'
  nodeModuleFilePatterns:
    - '**/*'
    - .dist/main/**/*
    - .dist/renderer/**/*
    - node_modules/**/*
    - '!node_modules/.cache/**/*'
    - '!python/utils/**/*'
appx:
  mapping: "[Files]\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\LICENSE.electron.txt\" \"app\\LICENSE.electron.txt\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\LICENSES.chromium.html\" \"app\\LICENSES.chromium.html\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\One Whispr.exe\" \"app\\One Whispr.exe\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\chrome_100_percent.pak\" \"app\\chrome_100_percent.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\chrome_200_percent.pak\" \"app\\chrome_200_percent.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\d3dcompiler_47.dll\" \"app\\d3dcompiler_47.dll\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\ffmpeg.dll\" \"app\\ffmpeg.dll\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\icudtl.dat\" \"app\\icudtl.dat\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\libEGL.dll\" \"app\\libEGL.dll\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\libGLESv2.dll\" \"app\\libGLESv2.dll\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources.pak\" \"app\\resources.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\snapshot_blob.bin\" \"app\\snapshot_blob.bin\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\v8_context_snapshot.bin\" \"app\\v8_context_snapshot.bin\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\vk_swiftshader.dll\" \"app\\vk_swiftshader.dll\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\vk_swiftshader_icd.json\" \"app\\vk_swiftshader_icd.json\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\vulkan-1.dll\" \"app\\vulkan-1.dll\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar\" \"app\\resources\\app.asar\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\one.whispr-black.png\" \"app\\resources\\one.whispr-black.png\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\one.whispr-white.png\" \"app\\resources\\one.whispr-white.png\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\sounds\\whispr-cancelled.mp3\" \"app\\resources\\sounds\\whispr-cancelled.mp3\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\sounds\\whispr-disable.mp3\" \"app\\resources\\sounds\\whispr-disable.mp3\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\sounds\\whispr-enable.mp3\" \"app\\resources\\sounds\\whispr-enable.mp3\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\models\\pyannote_speaker-diarization-3.1\\pipeline.pkl\" \"app\\resources\\models\\pyannote_speaker-diarization-3.1\\pipeline.pkl\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\models\\pyannote_segmentation-3.0\\model_complete.pkl\" \"app\\resources\\models\\pyannote_segmentation-3.0\\model_complete.pkl\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\models\\pyannote_segmentation-3.0\\pytorch_model.bin\" \"app\\resources\\models\\pyannote_segmentation-3.0\\pytorch_model.bin\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\models\\pyannote_embedding\\model_complete.pkl\" \"app\\resources\\models\\pyannote_embedding\\model_complete.pkl\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\models\\pyannote_embedding\\pytorch_model.bin\" \"app\\resources\\models\\pyannote_embedding\\pytorch_model.bin\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\backend\\OneWhispr-Runtime-Base.7z\" \"app\\resources\\backend\\OneWhispr-Runtime-Base.7z\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\backend\\OneWhispr-Scripts.7z\" \"app\\resources\\backend\\OneWhispr-Scripts.7z\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\LICENSE\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\LICENSE\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\package.json\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\package.json\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\src\\better_sqlite3.cpp\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\src\\better_sqlite3.cpp\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\src\\better_sqlite3.hpp\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\src\\better_sqlite3.hpp\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\database.js\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\database.js\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\index.js\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\index.js\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\sqlite-error.js\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\sqlite-error.js\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\util.js\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\util.js\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\methods\\aggregate.js\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\methods\\aggregate.js\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\methods\\backup.js\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\methods\\backup.js\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\methods\\function.js\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\methods\\function.js\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\methods\\inspect.js\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\methods\\inspect.js\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\methods\\pragma.js\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\methods\\pragma.js\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\methods\\serialize.js\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\methods\\serialize.js\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\methods\\table.js\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\methods\\table.js\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\methods\\transaction.js\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\methods\\transaction.js\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\methods\\wrappers.js\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\lib\\methods\\wrappers.js\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\deps\\common.gypi\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\deps\\common.gypi\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\deps\\copy.js\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\deps\\copy.js\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\deps\\defines.gypi\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\deps\\defines.gypi\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\deps\\download.sh\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\deps\\download.sh\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\deps\\sqlite3.gyp\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\deps\\sqlite3.gyp\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\deps\\test_extension.c\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\deps\\test_extension.c\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\deps\\sqlite3\\sqlite3.c\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\deps\\sqlite3\\sqlite3.c\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\deps\\sqlite3\\sqlite3.h\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\deps\\sqlite3\\sqlite3.h\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\deps\\sqlite3\\sqlite3ext.h\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\deps\\sqlite3\\sqlite3ext.h\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\deps\\patches\\1208.patch\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\deps\\patches\\1208.patch\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\build\\Release\\better_sqlite3.node\" \"app\\resources\\app.asar.unpacked\\node_modules\\better-sqlite3\\build\\Release\\better_sqlite3.node\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\af.pak\" \"app\\locales\\af.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\am.pak\" \"app\\locales\\am.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\ar.pak\" \"app\\locales\\ar.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\bg.pak\" \"app\\locales\\bg.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\bn.pak\" \"app\\locales\\bn.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\ca.pak\" \"app\\locales\\ca.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\cs.pak\" \"app\\locales\\cs.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\da.pak\" \"app\\locales\\da.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\de.pak\" \"app\\locales\\de.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\el.pak\" \"app\\locales\\el.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\en-GB.pak\" \"app\\locales\\en-GB.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\en-US.pak\" \"app\\locales\\en-US.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\es-419.pak\" \"app\\locales\\es-419.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\es.pak\" \"app\\locales\\es.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\et.pak\" \"app\\locales\\et.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\fa.pak\" \"app\\locales\\fa.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\fi.pak\" \"app\\locales\\fi.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\fil.pak\" \"app\\locales\\fil.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\fr.pak\" \"app\\locales\\fr.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\gu.pak\" \"app\\locales\\gu.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\he.pak\" \"app\\locales\\he.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\hi.pak\" \"app\\locales\\hi.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\hr.pak\" \"app\\locales\\hr.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\hu.pak\" \"app\\locales\\hu.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\id.pak\" \"app\\locales\\id.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\it.pak\" \"app\\locales\\it.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\ja.pak\" \"app\\locales\\ja.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\kn.pak\" \"app\\locales\\kn.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\ko.pak\" \"app\\locales\\ko.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\lt.pak\" \"app\\locales\\lt.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\lv.pak\" \"app\\locales\\lv.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\ml.pak\" \"app\\locales\\ml.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\mr.pak\" \"app\\locales\\mr.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\ms.pak\" \"app\\locales\\ms.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\nb.pak\" \"app\\locales\\nb.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\nl.pak\" \"app\\locales\\nl.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\pl.pak\" \"app\\locales\\pl.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\pt-BR.pak\" \"app\\locales\\pt-BR.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\pt-PT.pak\" \"app\\locales\\pt-PT.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\ro.pak\" \"app\\locales\\ro.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\ru.pak\" \"app\\locales\\ru.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\sk.pak\" \"app\\locales\\sk.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\sl.pak\" \"app\\locales\\sl.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\sr.pak\" \"app\\locales\\sr.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\sv.pak\" \"app\\locales\\sv.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\sw.pak\" \"app\\locales\\sw.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\ta.pak\" \"app\\locales\\ta.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\te.pak\" \"app\\locales\\te.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\th.pak\" \"app\\locales\\th.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\tr.pak\" \"app\\locales\\tr.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\uk.pak\" \"app\\locales\\uk.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\ur.pak\" \"app\\locales\\ur.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\vi.pak\" \"app\\locales\\vi.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\zh-CN.pak\" \"app\\locales\\zh-CN.pak\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\win-unpacked\\locales\\zh-TW.pak\" \"app\\locales\\zh-TW.pak\"\r\n\"C:\\Users\\<USER>\\AppData\\Local\\electron-builder\\Cache\\winCodeSign\\winCodeSign-2.6.0\\appxAssets\\SampleAppx.50x50.png\" \"assets\\StoreLogo.png\"\r\n\"C:\\Users\\<USER>\\AppData\\Local\\electron-builder\\Cache\\winCodeSign\\winCodeSign-2.6.0\\appxAssets\\SampleAppx.150x150.png\" \"assets\\Square150x150Logo.png\"\r\n\"C:\\Users\\<USER>\\AppData\\Local\\electron-builder\\Cache\\winCodeSign\\winCodeSign-2.6.0\\appxAssets\\SampleAppx.44x44.png\" \"assets\\Square44x44Logo.png\"\r\n\"C:\\Users\\<USER>\\AppData\\Local\\electron-builder\\Cache\\winCodeSign\\winCodeSign-2.6.0\\appxAssets\\SampleAppx.310x150.png\" \"assets\\Wide310x150Logo.png\"\r\n\"C:\\Users\\<USER>\\source\\one.whispr\\one.whispr-app\\.release-microsoft\\__appx-x64\\AppxManifest.xml\" \"AppxManifest.xml\""
