import { initializeDatabaseSystem, shutdownDatabaseSystem, sendInitialSync } from '../database';
import { setupAppIPCHandlers } from './app/ipc';
import { setupVocabularyIPCHandlers } from './vocabulary/ipc';
import { setupTextReplacementIPCHandlers } from './text-replacements/ipc';
import { setupModeIPCHandlers } from './modes/ipc';
import { setupSettingsIPCHandlers } from './settings/ipc';
import { setupAIModelIPCHandlers } from './ai-models/ipc';
import { initializeBackend, terminateBackend } from '../../python/main';
import { ipcMain } from 'electron';
import { getSettingsWindow } from '../windows';
import { sendRequest, isBackendConnected, isBackendRunning, getBackendPort, backendEvents } from '../../python/main';

/**
 * Sets up initial database sync (without window dependency)
 */
export function setupInitialDatabaseSync(): Promise<void> {
  return new Promise((resolve) => {
    // Set up one-time listener for backend connection
    const onConnected = () => {
      console.log('[MAIN] Backend connected, starting initial database sync...');
      
      sendInitialSync().then(() => {
        console.log('[MAIN] Initial database sync completed successfully');
        resolve();
      }).catch((error: Error) => {
        console.error('[MAIN] Failed to send initial database sync to backend:', error);
        // Still resolve so the app continues (user can see error in UI)
        resolve();
      });
    };

    // One-time event listener
    backendEvents.once('connected', onConnected);
    
    // If already connected, trigger immediately
    if (isBackendConnected()) {
      onConnected();
    }
  });
}

/**
 * Sets up forwarding of backend events to the renderer process
 * (requires window to exist)
 */
export function setupBackendEventForwarding(): void {
  const window = getSettingsWindow();
  if (!window) {
    console.warn('No settings window available for Python event forwarding');
    return;
  }

  // Helper function to safely send messages to renderer
  const safeSend = (channel: string, data?: any) => {
    try {
      const currentWindow = getSettingsWindow();
      if (currentWindow && !currentWindow.isDestroyed() && currentWindow.webContents && !currentWindow.webContents.isDestroyed()) {
        if (data !== undefined) {
          currentWindow.webContents.send(channel, data);
        } else {
          currentWindow.webContents.send(channel);
        }
      }
    } catch (error) {
      // Silently ignore errors during shutdown
    }
  };

  // Core events from backend
  backendEvents.on('connected', () => {
    safeSend('python-ws-connected');
    console.log('[MAIN] Backend WebSocket connected - events will be automatically forwarded');
  });

  backendEvents.on('disconnected', () => {
    safeSend('python-ws-connection-failed');
  });

  backendEvents.on('reconnect_failed', () => {
    safeSend('python-ws-connection-failed');
  });

  // The 'message' event contains all messages from backend
  backendEvents.on('message', (message: any) => {
    // Forward all messages, including our new transcription events
    safeSend('python-ws-message', message);
    console.log('Received WebSocket message:', JSON.stringify(message, null, 2));
  });

  // Setup event listeners for any other dynamic events
  backendEvents.on('server_welcome', (data: any) => {
    safeSend('python-server-welcome', data);
  });

  // ML library initialization events
  backendEvents.on('ml_libraries_initialization_started', (data: any) => {
    safeSend('python-ml-libraries-initialization-started', data);
    console.log('[MAIN] ML libraries initialization started event forwarded to renderer');
  });
  
  backendEvents.on('ml_libraries_initialized', (data: any) => {
    safeSend('python-ml-libraries-initialized', data);
    console.log('[MAIN] ML libraries initialization completed event forwarded to renderer');
  });
  
  backendEvents.on('ml_libraries_initialization_failed', (data: any) => {
    safeSend('python-ml-libraries-initialization-failed', data);
    console.log('[MAIN] ML libraries initialization failed event forwarded to renderer');
  });
}

/**
 * Sets up all backend-related IPC handlers
 * Should be called during app initialization
 */
export function setupBackendIPCHandlers(): void {
  // Main command handler for backend
  ipcMain.handle('send-command', async (_event, type: string, args?: any) => {
    try {
      // Check if backend is connected
      if (!isBackendConnected()) {
        console.log(`Backend not connected, command ${type} will be rejected`);
        // For certain commands, return a specific fallback response
        if (type === 'system.status' || type === 'system.ping') {
          return {
            status: 'error',
            message: 'Backend not connected',
            pythonConnected: false
          };
        }
        
        // For other commands, return an error
        return {
          status: 'error',
          message: 'Backend not connected',
          command: type
        };
      }
      
      // Use longer timeout for model loading operations
      const timeout = type === 'models.load' ? 120000 : 30000; // 2 minutes for model loading, 30s for others
      
      return await sendRequest(type, args || {}, timeout);
    } catch (error) {
      console.error(`Error sending command ${type} to backend:`, error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        command: type
      };
    }
  });
  
  // Handle getting backend status
  ipcMain.handle('python-get-status', () => {
    return {
      pythonRunning: isBackendRunning(),
      pythonConnected: isBackendConnected(),
      port: getBackendPort()
    };
  });

  // Handle backend reconnection
  ipcMain.handle('python-reconnect', async () => {
    try {
      // For now, we'll just return the current status since backend doesn't 
      // export a reconnect function we can call
      return {
        status: 'success',
        pythonConnected: isBackendConnected()
      };
    } catch (error) {
      console.error('Error reconnecting to backend:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Note: setupBackendEventForwarding() is now called separately from main.ts
  // after the settings window is created to ensure proper initialization order
}

/**
 * Initialize all application features
 */
export async function initializeFeatures(): Promise<void> {
  try {
    // Initialize the database system
    initializeDatabaseSystem();
    
    // Set up IPC handlers
    setupBackendIPCHandlers();
    setupAppIPCHandlers();
    setupVocabularyIPCHandlers();
    setupTextReplacementIPCHandlers();
    setupModeIPCHandlers();
    setupSettingsIPCHandlers();
    setupAIModelIPCHandlers();
    
    // Initialize the backend with enhanced error handling
    try {
      await initializeBackend();
      console.log('Backend initialized successfully');
    } catch (backendError) {
      console.error('Failed to initialize backend:', backendError);
      // Don't throw here - allow the app to continue without backend
      // The UI will show connection status and user can retry
      console.log('Application will continue without backend - user can retry connection');
    }
    
    console.log('Features initialized successfully');
  } catch (error) {
    console.error('Error initializing features:', error);
    throw error;
  }
}

/**
 * Clean up all application features
 */
export function disposeFeatures(): void {
  try {
    // Clean up backend
    terminateBackend();
    
    // Close database connections
    shutdownDatabaseSystem();
    
    console.log('Features disposed successfully');
  } catch (error) {
    console.error('Error disposing features:', error);
  }
}