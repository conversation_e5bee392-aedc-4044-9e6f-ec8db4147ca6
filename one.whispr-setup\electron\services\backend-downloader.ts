import { ipcMain } from 'electron';
import * as fs from 'fs-extra';
import * as path from 'path';
import axios from 'axios';
import { spawn } from 'child_process';
import sevenBin from '7zip-bin';
import { getLauncherWindow } from '../managers/window';
import { BACKEND_UPDATES } from '../../src/lib/constants';
import { downloader } from './main-app-downloader';

// Types
interface BackendVersionInfo {
  version: string;
  releaseDate: string;
  releaseNotes: string;
  downloadUrl: string;
  compressionType?: string;
  updateType?: string;
  isRollback?: boolean;  // For rollbacks
  checksum?: string;     // SHA256 checksum of the 7z file
}

interface BackendDownloadProgress {
  type: 'runtime' | 'scripts';
  progress: number;
  speed: number;
  eta: number;
  status: string;
}

/**
 * Backend downloader that handles separated builds
 * - Base Runtime: ~1.3GB (downloaded once, rarely updated)
 * - Scripts Code: ~200KB (frequent updates)
 */
class BackendDownloader {
  private backendPath: string;
  private isDownloading: boolean = false;
  private abortController: AbortController | null = null;
  
  constructor() {
    // Set backend path to MainApp/resources/backend (where the main app expects it)
    const mainAppPath = downloader.getDownloadPath();
    this.backendPath = path.join(mainAppPath, 'resources', 'backend');

    // Create backend directory if it doesn't exist (this will also create MainApp if needed)
    fs.ensureDirSync(this.backendPath);

    // Setup IPC handlers
    this.setupIpcHandlers();
  }
  
  /**
   * Setup IPC handlers for backend download-related events
   */
  private setupIpcHandlers(): void {
    // Check if backend update is needed
    ipcMain.handle('backend:check-update', async () => {
      return this.checkBackendUpdate();
    });

    // Start backend download/update
    ipcMain.handle('backend:download', async () => {
      return this.downloadBackend();
    });

    // Cancel backend download
    ipcMain.handle('backend:cancel', () => {
      return this.cancelDownload();
    });
  }
  
  /**
   * Check if backend update is needed
   */
  public async checkBackendUpdate(): Promise<{
    runtimeNeeded: boolean,
    scriptsNeeded: boolean,
    runtimeVersion?: string,
    scriptsVersion?: string,
    reason: string
  }> {
    try {
      // Check runtime version
      const runtimeInfo = await this.fetchVersionInfo('runtime');
      const runtimeNeeded = await this.isRuntimeUpdateNeeded(runtimeInfo);
      
      // Check scripts version
      const scriptsInfo = await this.fetchVersionInfo('scripts');
      const scriptsNeeded = await this.isScriptsUpdateNeeded(scriptsInfo);
      
      let reason = 'All backend components up to date';
      if (runtimeNeeded && scriptsNeeded) {
        reason = 'First time installation - downloading base runtime and scripts';
      } else if (runtimeNeeded) {
        reason = 'Base runtime update available';
      } else if (scriptsNeeded) {
        reason = 'Backend scripts update available';
      }

      return {
        runtimeNeeded,
        scriptsNeeded,
        runtimeVersion: runtimeInfo?.version,
        scriptsVersion: scriptsInfo?.version,
        reason
      };
    } catch (error) {
      console.error('[BACKEND] Error checking updates:', error);
      return {
        runtimeNeeded: false,
        scriptsNeeded: false,
        reason: 'Error checking updates - will try to use existing backend'
      };
    }
  }
  
  /**
   * Download backend components (runtime and/or scripts)
   */
  public async downloadBackend(): Promise<boolean> {
    console.log('[BACKEND] downloadBackend() called');

    if (this.isDownloading) {
      console.log('[BACKEND] Download already in progress');
      return false;
    }

    try {
      console.log('[BACKEND] Starting download process...');
      this.isDownloading = true;
      this.abortController = new AbortController();

      // Check what needs to be downloaded
      console.log('[BACKEND] Checking what needs to be downloaded...');
      const updateCheck = await this.checkBackendUpdate();
      console.log('[BACKEND] Update check result:', updateCheck);
      
      if (!updateCheck.runtimeNeeded && !updateCheck.scriptsNeeded) {
        console.log('[BACKEND] No updates needed');
        this.isDownloading = false;
        this.abortController = null;
        return true;
      }

      // Download base runtime if needed
      if (updateCheck.runtimeNeeded) {
        await this.downloadRuntime();
      }

      // Download scripts (always download to ensure latest)
      if (updateCheck.scriptsNeeded || updateCheck.runtimeNeeded) {
        await this.downloadScripts();
      }

      // Download Whisper base model as final step
      await this.downloadWhisperBaseModel();

      this.isDownloading = false;
      this.abortController = null;
      
      // Notify completion
      const launcherWindow = getLauncherWindow();
      if (launcherWindow) {
        launcherWindow.webContents.send('backend:complete');
      }
      
      return true;
    } catch (error) {
      console.error('[BACKEND] Download error:', error);
      
      this.isDownloading = false;
      this.abortController = null;
      
      // Notify error
      const launcherWindow = getLauncherWindow();
      if (launcherWindow) {
        launcherWindow.webContents.send('backend:error', {
          message: error instanceof Error ? error.message : String(error)
        });
      }
      
      return false;
    }
  }
  
  /**
   * Fetch version information for runtime or scripts
   */
  private async fetchVersionInfo(type: 'runtime' | 'scripts'): Promise<BackendVersionInfo | null> {
    try {
      const url = BACKEND_UPDATES[type].versionUrl;
      console.log(`[BACKEND] Fetching ${type} version from ${url}`);

      const response = await axios.get(url, {
        timeout: 30000,
        headers: { 'User-Agent': 'OneWhispr-Setup/1.0.0' }
      });

      return response.data as BackendVersionInfo;
    } catch (error) {
      console.error(`[BACKEND] Error fetching ${type} version:`, error);
      return null;
    }
  }
  
  /**
   * Check if runtime update is needed
   */
  private async isRuntimeUpdateNeeded(runtimeInfo: BackendVersionInfo | null): Promise<boolean> {
    if (!runtimeInfo) {
      return true; // First install
    }

    const exePath = path.join(this.backendPath, 'One Whispr Backend.exe');
    if (!fs.existsSync(exePath)) {
      return true; // Backend executable missing
    }

    const versionPath = path.join(this.backendPath, 'runtime-version.json');
    if (!fs.existsSync(versionPath)) {
      return true;
    }
    
    try {
      const currentVersion = await fs.readJson(versionPath);

      // For rollbacks, be extra careful with runtime (1.2GB download)
      if (runtimeInfo.isRollback === true) {
        // Compare checksums to see if runtime actually changed
        if (currentVersion.checksum && runtimeInfo.checksum) {
          if (currentVersion.checksum !== runtimeInfo.checksum) {
            console.log(`[BACKEND] Runtime rollback required - checksum changed`);
            console.log(`[BACKEND] Current: ${currentVersion.checksum}`);
            console.log(`[BACKEND] Server: ${runtimeInfo.checksum}`);
            return true;
          } else {
            console.log(`[BACKEND] Runtime rollback skipped - same checksum (no actual changes)`);
            return false;
          }
        } else {
          // No checksums available - compare versions
          if (currentVersion.version !== runtimeInfo.version) {
            console.log(`[BACKEND] Runtime rollback: ${currentVersion.version} → ${runtimeInfo.version} (no checksum available)`);
            return true;
          } else {
            console.log(`[BACKEND] Runtime rollback skipped - same version (${runtimeInfo.version})`);
            return false;
          }
        }
      }

      // Compare release dates - update if server has newer release date
      const currentDate = new Date(currentVersion.releaseDate);
      const serverDate = new Date(runtimeInfo.releaseDate);
      return serverDate > currentDate;
    } catch {
      return true;
    }
  }
  
  /**
   * Check if scripts update is needed
   */
  private async isScriptsUpdateNeeded(scriptsInfo: BackendVersionInfo | null): Promise<boolean> {
    if (!scriptsInfo) {
      return true; // First install
    }

    const scriptsPath = path.join(this.backendPath, 'scripts');

    // Check if main.pyc exists in scripts directory
    const mainPycPath = path.join(scriptsPath, 'main.pyc');
    if (!fs.existsSync(mainPycPath)) {
      return true;
    }

    // Check version file in scripts directory
    const versionPath = path.join(scriptsPath, 'scripts-version.json');
    if (!fs.existsSync(versionPath)) {
      return true;
    }

    try {
      const currentVersion = await fs.readJson(versionPath);

      // Check for rollback flag (forces update regardless of date)
      if (scriptsInfo.isRollback === true) {
        return true;
      }

      // Compare release dates - update if server has newer release date
      const currentDate = new Date(currentVersion.releaseDate);
      const serverDate = new Date(scriptsInfo.releaseDate);
      return serverDate > currentDate;
    } catch {
      return true;
    }
  }
  
  /**
   * Download and extract base runtime
   */
  private async downloadRuntime(): Promise<void> {
    const runtimeInfo = await this.fetchVersionInfo('runtime');
    if (!runtimeInfo) {
      throw new Error('Failed to fetch runtime version info');
    }
    
    this.sendProgress('runtime', 0, 0, 0, 'Downloading CUDA runtime...');
    
    // Download the 7z archive
    const archivePath = path.join(this.backendPath, 'OneWhispr-Runtime-Base.7z');
    await this.downloadFile(BACKEND_UPDATES.runtime.archiveUrl, archivePath, 'runtime');
    
    this.sendProgress('runtime', 90, 0, 0, 'Extracting CUDA runtime...');

    // Extract the archive
    await this.extract7z(archivePath, this.backendPath);

    // Clean up archive
    await fs.remove(archivePath);
    
    // Save version info
    await fs.writeJson(path.join(this.backendPath, 'runtime-version.json'), {
      version: runtimeInfo.version,
      releaseDate: runtimeInfo.releaseDate,
      checksum: runtimeInfo.checksum,  // Save checksum for future comparisons
      installedAt: new Date().toISOString()
    });
    
    this.sendProgress('runtime', 100, 0, 0, 'CUDA runtime installed');
  }
  
  /**
   * Download and extract scripts code
   */
  private async downloadScripts(): Promise<void> {
    const scriptsInfo = await this.fetchVersionInfo('scripts');
    if (!scriptsInfo) {
      throw new Error('Failed to fetch scripts version info');
    }

    this.sendProgress('scripts', 0, 0, 0, 'Downloading backend scripts...');

    // Create scripts directory
    const scriptsPath = path.join(this.backendPath, 'scripts');

    // Clean up old scripts files first
    await this.cleanupScriptsFiles();

    // Download the 7z archive
    const archivePath = path.join(this.backendPath, 'OneWhispr-Scripts.7z');
    await this.downloadFile(BACKEND_UPDATES.scripts.archiveUrl, archivePath, 'scripts');

    this.sendProgress('scripts', 90, 0, 0, 'Extracting backend scripts...');

    // Extract the archive to scripts directory
    await this.extract7z(archivePath, scriptsPath);

    // Clean up archive
    await fs.remove(archivePath);

    // Save version info in scripts directory
    await fs.writeJson(path.join(scriptsPath, 'scripts-version.json'), {
      version: scriptsInfo.version,
      releaseDate: scriptsInfo.releaseDate,
      installedAt: new Date().toISOString()
    });

    this.sendProgress('scripts', 100, 0, 0, 'Backend scripts updated');
  }

  /**
   * Download Whisper base model as final installation step
   */
  private async downloadWhisperBaseModel(): Promise<void> {
    try {
      console.log('[MODELS] Starting Whisper base model download...');

      // Define Whisper base model files from model_registry.py
      const whisperBaseFiles = [
        { name: 'model.safetensors', size: 290403936, url: 'https://huggingface.co/openai/whisper-base/resolve/main/model.safetensors' },
        { name: 'config.json', size: 1983, url: 'https://huggingface.co/openai/whisper-base/resolve/main/config.json' },
        { name: 'tokenizer.json', size: 2480466, url: 'https://huggingface.co/openai/whisper-base/resolve/main/tokenizer.json' },
        { name: 'generation_config.json', size: 3807, url: 'https://huggingface.co/openai/whisper-base/resolve/main/generation_config.json' },
        { name: 'tokenizer_config.json', size: 282683, url: 'https://huggingface.co/openai/whisper-base/resolve/main/tokenizer_config.json' },
        { name: 'preprocessor_config.json', size: 184990, url: 'https://huggingface.co/openai/whisper-base/resolve/main/preprocessor_config.json' },
        { name: 'added_tokens.json', size: 34604, url: 'https://huggingface.co/openai/whisper-base/resolve/main/added_tokens.json' },
        { name: 'normalizer.json', size: 52666, url: 'https://huggingface.co/openai/whisper-base/resolve/main/normalizer.json' },
        { name: 'merges.txt', size: 493869, url: 'https://huggingface.co/openai/whisper-base/resolve/main/merges.txt' },
        { name: 'special_tokens_map.json', size: 2194, url: 'https://huggingface.co/openai/whisper-base/resolve/main/special_tokens_map.json' },
        { name: 'vocab.json', size: 835550, url: 'https://huggingface.co/openai/whisper-base/resolve/main/vocab.json' }
      ];

      // Check if model already exists and is complete
      const mainAppPath = downloader.getDownloadPath();
      const voiceModelsPath = path.join(mainAppPath, 'resources', 'voicemodels');
      const whisperBasePath = path.join(voiceModelsPath, 'openai--whisper-base');

      // Check if all required files exist with correct sizes
      if (fs.existsSync(whisperBasePath)) {
        console.log('[MODELS] Checking existing Whisper base model...');

        let allFilesValid = true;
        let existingFiles = 0;

        for (const file of whisperBaseFiles) {
          const filePath = path.join(whisperBasePath, file.name);
          if (fs.existsSync(filePath)) {
            const stats = fs.statSync(filePath);
            if (stats.size === file.size) {
              existingFiles++;
            } else {
              console.log(`[MODELS] File ${file.name} has incorrect size (${stats.size} vs ${file.size})`);
              allFilesValid = false;
              break;
            }
          } else {
            console.log(`[MODELS] Missing file: ${file.name}`);
            allFilesValid = false;
            break;
          }
        }

        if (allFilesValid && existingFiles === whisperBaseFiles.length) {
          console.log(`[MODELS] Whisper base model is complete (${existingFiles}/${whisperBaseFiles.length} files), skipping download`);
          this.sendProgress('scripts', 100, 0, 0, 'Whisper base model already available');
          return;
        } else {
          console.log(`[MODELS] Whisper base model incomplete (${existingFiles}/${whisperBaseFiles.length} files valid), will download missing files`);
        }
      } else {
        console.log('[MODELS] Whisper base model not found, starting fresh download');
      }

      this.sendProgress('scripts', 0, 0, 0, 'Downloading Whisper base model...');

      // Ensure voicemodels directory exists
      await fs.ensureDir(whisperBasePath);

      const totalSize = whisperBaseFiles.reduce((sum, file) => sum + file.size, 0);
      let downloadedSize = 0;

      console.log(`[MODELS] Downloading ${whisperBaseFiles.length} files (${Math.round(totalSize / 1024 / 1024)}MB)...`);

      // Download each file
      for (let i = 0; i < whisperBaseFiles.length; i++) {
        const file = whisperBaseFiles[i];
        const filePath = path.join(whisperBasePath, file.name);

        // Skip if file already exists and has correct size
        if (fs.existsSync(filePath)) {
          const stats = fs.statSync(filePath);
          if (stats.size === file.size) {
            console.log(`[MODELS] File ${file.name} already exists with correct size`);
            downloadedSize += file.size;
            continue;
          }
        }

        console.log(`[MODELS] Downloading ${file.name} (${Math.round(file.size / 1024 / 1024)}MB)...`);

        const progress = Math.round((downloadedSize / totalSize) * 100);
        this.sendProgress('scripts', progress, 0, 0, `Downloading ${file.name}...`);

        await this.downloadFile(file.url, filePath, 'scripts');
        downloadedSize += file.size;
      }

      this.sendProgress('scripts', 100, 0, 0, 'Whisper base model ready');
      console.log('[MODELS] Whisper base model download completed successfully');

    } catch (error) {
      console.error('[MODELS] Error downloading Whisper base model:', error);
      // Don't throw - this is not critical for app functionality
      this.sendProgress('scripts', 0, 0, 0, 'Whisper model download failed (will download on first use)');
    }
  }

  /**
   * Clean up old scripts files
   */
  private async cleanupScriptsFiles(): Promise<void> {
    try {
      const scriptsPath = path.join(this.backendPath, 'scripts');

      // Remove the entire scripts directory if it exists
      if (fs.existsSync(scriptsPath)) {
        await fs.remove(scriptsPath);
        console.log('[BACKEND] Cleaned up old scripts directory');
      }

      // Ensure the scripts directory exists
      await fs.ensureDir(scriptsPath);
    } catch (error) {
      console.warn('[BACKEND] Error cleaning up scripts files:', error);
      // Don't throw - this is not critical
    }
  }

  /**
   * Download a file with progress reporting
   */
  private async downloadFile(url: string, filePath: string, type: 'runtime' | 'scripts'): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        console.log(`[BACKEND] Downloading ${type} from ${url}`);

        // Ensure directory exists
        await fs.ensureDir(path.dirname(filePath));

        // Create write stream
        const writer = fs.createWriteStream(filePath);

        // Track progress
        let downloadedBytes = 0;
        const startTime = Date.now();
        let lastUpdateTime = startTime;
        let lastBytes = 0;
        let speed = 0;

        // Download the file
        const response = await axios({
          method: 'get',
          url,
          responseType: 'stream',
          signal: this.abortController?.signal,
          timeout: 300000, // 5 minute timeout for large files
          headers: { 'User-Agent': 'OneWhispr-Setup/1.0.0' }
        });

        const totalSize = parseInt(response.headers['content-length'] || '0');

        response.data.on('data', (chunk: Buffer) => {
          downloadedBytes += chunk.length;

          // Calculate progress
          const progress = totalSize > 0 ? Math.min(90, Math.round((downloadedBytes / totalSize) * 90)) : 0;

          // Calculate speed and ETA
          const now = Date.now();
          const timeDiff = (now - lastUpdateTime) / 1000;

          if (timeDiff >= 1) { // Update every second
            speed = Math.round((downloadedBytes - lastBytes) / timeDiff);
            lastBytes = downloadedBytes;
            lastUpdateTime = now;
          }

          // Calculate ETA
          const remainingBytes = totalSize - downloadedBytes;
          const eta = speed > 0 ? Math.round(remainingBytes / speed) : 0;

          // Send progress update
          this.sendProgress(type, progress, speed, eta, `Downloading ${type}...`);
        });

        // Handle completion
        response.data.pipe(writer);

        writer.on('finish', resolve);
        writer.on('error', reject);
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Extract 7z archive using 7zip-bin
   */
  private async extract7z(archivePath: string, extractPath: string): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        console.log(`[BACKEND] Extracting ${archivePath} to ${extractPath}`);

        // Verify the archive file exists and is readable
        if (!fs.existsSync(archivePath)) {
          throw new Error(`Archive file does not exist: ${archivePath}`);
        }

        const archiveStats = await fs.stat(archivePath);
        if (archiveStats.size === 0) {
          throw new Error(`Archive file is empty: ${archivePath}`);
        }

        console.log(`[BACKEND] Archive size: ${Math.round(archiveStats.size / 1024 / 1024)}MB`);

        // Ensure extraction directory exists and is writable
        await fs.ensureDir(extractPath);

        // Test if extraction directory is writable
        const testFile = path.join(extractPath, 'test-write.tmp');
        try {
          await fs.writeFile(testFile, 'test');
          await fs.remove(testFile);
        } catch (writeError) {
          throw new Error(`Extraction directory is not writable: ${extractPath}`);
        }

        const sevenZipPath = sevenBin.path7za;
        console.log(`[BACKEND] Using 7zip binary: ${sevenZipPath}`);
        
        // More robust extraction arguments
        const args = [
          'x',                    // extract command
          archivePath,           // source file
          `-o${extractPath}`,    // output directory (no space after -o)
          '-y',                  // yes to all prompts
          '-aoa',                // overwrite all files without prompting
          '-bd'                  // disable progress indicator (cleaner output)
        ];

        console.log(`[BACKEND] Running command: ${sevenZipPath} ${args.join(' ')}`);

        let stdout = '';
        let stderr = '';

        const process = spawn(sevenZipPath, args, {
          stdio: ['pipe', 'pipe', 'pipe'],
          windowsHide: true
        });

        // Capture output for debugging
        process.stdout?.on('data', (data) => {
          stdout += data.toString();
        });

        process.stderr?.on('data', (data) => {
          stderr += data.toString();
        });

        process.on('close', (code) => {
          console.log(`[BACKEND] 7zip process exited with code: ${code}`);
          if (stdout) console.log(`[BACKEND] 7zip stdout: ${stdout}`);
          if (stderr) console.log(`[BACKEND] 7zip stderr: ${stderr}`);

          if (code === 0) {
            console.log('[BACKEND] Extraction completed successfully');
            resolve();
          } else {
            // Provide more detailed error information
            let errorMessage = `7zip extraction failed with code ${code}`;
            if (stderr) errorMessage += `\nstderr: ${stderr}`;
            if (stdout) errorMessage += `\nstdout: ${stdout}`;
            
            // Common 7zip error codes
            switch (code) {
              case 1:
                errorMessage += '\n(Warning: Non-fatal errors occurred)';
                // For code 1, we might still want to continue if files were extracted
                console.warn('[BACKEND] 7zip warning, but continuing...');
                resolve();
                return;
              case 2:
                errorMessage += '\n(Fatal error: Archive corrupted or not found)';
                break;
              case 7:
                errorMessage += '\n(Command line error)';
                break;
              case 8:
                errorMessage += '\n(Not enough memory)';
                break;
              case 255:
                errorMessage += '\n(User stopped the process)';
                break;
            }
            
            reject(new Error(errorMessage));
          }
        });

        process.on('error', (error) => {
          reject(new Error(`7zip process error: ${error.message}`));
        });

        // Set a timeout for very large files (30 minutes)
        setTimeout(() => {
          if (!process.killed) {
            process.kill();
            reject(new Error('7zip extraction timed out after 30 minutes'));
          }
        }, 30 * 60 * 1000);

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Send progress update to renderer
   */
  private sendProgress(type: 'runtime' | 'scripts', progress: number, speed: number, eta: number, status: string): void {
    const launcherWindow = getLauncherWindow();
    if (launcherWindow) {
      launcherWindow.webContents.send('backend:progress', {
        type,
        progress,
        speed,
        eta,
        status
      } as BackendDownloadProgress);
    }
  }

  /**
   * Cancel the current download
   */
  public cancelDownload(): boolean {
    if (!this.isDownloading || !this.abortController) {
      return false;
    }

    console.log('[BACKEND] Cancelling download');

    this.abortController.abort();
    this.isDownloading = false;
    this.abortController = null;

    return true;
  }

  /**
   * Get the backend installation path
   */
  public getBackendPath(): string {
    return this.backendPath;
  }

  /**
   * Check if backend is ready to use
   */
  public async isBackendReady(): Promise<boolean> {
    const exePath = path.join(this.backendPath, 'One Whispr Backend.exe');
    const scriptsPath = path.join(this.backendPath, 'scripts');
    const mainPycPath = path.join(scriptsPath, 'main.pyc');
    const whisprPath = path.join(scriptsPath, 'whispr');

    return fs.existsSync(exePath) &&
           fs.existsSync(mainPycPath) &&
           fs.existsSync(whisprPath);
  }
}

// Export singleton instance
export const backendDownloader = new BackendDownloader();
